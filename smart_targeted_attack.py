#!/usr/bin/env python3
"""
智能定向攻击工具
基于Flutter钱包和区块链钱包的常见实现模式
"""

import base64
import json
import xml.etree.ElementTree as ET
import hashlib
import binascii
import hmac
import struct
from ecdsa import SigningKey, SECP256k1
from Crypto.Hash import keccak
from Crypto.Cipher import AES
from Crypto.Protocol.KDF import PBKDF2, scrypt
from Crypto.Util.Padding import unpad

TARGET_ADDRESS = "******************************************"
WALLET_PASSWORDS = ["zyuu23521", "zy96669", "2221237"]

def private_key_to_address(private_key_hex):
    """从私钥生成以太坊地址"""
    try:
        if len(private_key_hex) != 64:
            return None
        private_key_bytes = binascii.unhexlify(private_key_hex)
        sk = SigningKey.from_string(private_key_bytes, curve=SECP256k1)
        public_key = sk.get_verifying_key().to_string()
        
        keccak_hash = keccak.new(digest_bits=256)
        keccak_hash.update(public_key)
        address_bytes = keccak_hash.digest()[-20:]
        
        address = "0x" + binascii.hexlify(address_bytes).decode().lower()
        return address
    except:
        return None

def extract_encrypted_data():
    """提取加密数据"""
    prefs_file = "epik_data_final/apps/com.epik.wallet/sp/FlutterSharedPreferences.xml"
    tree = ET.parse(prefs_file)
    root = tree.getroot()
    
    for string_elem in root.findall('string'):
        name = string_elem.get('name')
        if name == 'flutter.am_account_list':
            encrypted_b64 = string_elem.text
            encrypted_data = base64.b64decode(encrypted_b64)
            return encrypted_data
    return None

def try_flutter_keystore_decryption(encrypted_data, password):
    """尝试Flutter Keystore常用的解密方式"""
    results = []
    
    print(f"尝试Flutter Keystore解密方式...")
    
    # Flutter通常使用的加密方案
    schemes = [
        # 方案1: PBKDF2 + AES-256-CBC (最常见)
        {
            "name": "Flutter-Standard-PBKDF2-AES-CBC",
            "salt_len": 16,
            "iv_len": 16,
            "key_derivation": lambda pwd, salt: PBKDF2(pwd, salt, 32, count=10000),
            "cipher_mode": AES.MODE_CBC
        },
        # 方案2: PBKDF2 + AES-256-ECB
        {
            "name": "Flutter-Standard-PBKDF2-AES-ECB", 
            "salt_len": 16,
            "iv_len": 0,
            "key_derivation": lambda pwd, salt: PBKDF2(pwd, salt, 32, count=10000),
            "cipher_mode": AES.MODE_ECB
        },
        # 方案3: Scrypt + AES-256-CBC (现代Flutter)
        {
            "name": "Flutter-Modern-Scrypt-AES-CBC",
            "salt_len": 32,
            "iv_len": 16, 
            "key_derivation": lambda pwd, salt: scrypt(pwd.encode(), salt, 32, N=16384, r=8, p=1),
            "cipher_mode": AES.MODE_CBC
        },
        # 方案4: 简单SHA256 + AES (调试/简化版本)
        {
            "name": "Flutter-Simple-SHA256-AES-CBC",
            "salt_len": 16,
            "iv_len": 16,
            "key_derivation": lambda pwd, salt: hashlib.sha256((pwd + salt.hex()).encode()).digest(),
            "cipher_mode": AES.MODE_CBC
        }
    ]
    
    for scheme in schemes:
        try:
            salt_len = scheme["salt_len"]
            iv_len = scheme["iv_len"]
            
            if len(encrypted_data) < salt_len + iv_len:
                continue
                
            salt = encrypted_data[:salt_len]
            
            if iv_len > 0:
                iv = encrypted_data[salt_len:salt_len + iv_len]
                ciphertext = encrypted_data[salt_len + iv_len:]
            else:
                iv = None
                ciphertext = encrypted_data[salt_len:]
            
            # 派生密钥
            key = scheme["key_derivation"](password, salt)
            
            # 解密
            if iv is not None:
                cipher = AES.new(key, scheme["cipher_mode"], iv)
            else:
                cipher = AES.new(key, scheme["cipher_mode"])
            
            decrypted = cipher.decrypt(ciphertext)
            
            # 尝试去除PKCS7填充
            try:
                decrypted_unpadded = unpad(decrypted, AES.block_size)
                results.append((scheme["name"] + "-padded", decrypted_unpadded))
            except:
                pass
            
            # 也尝试不去除填充的版本
            results.append((scheme["name"] + "-raw", decrypted))
            
        except Exception as e:
            print(f"方案 {scheme['name']} 失败: {e}")
            continue
    
    return results

def try_web3_wallet_patterns(encrypted_data, password):
    """尝试Web3钱包常见的加密模式"""
    results = []
    
    print(f"尝试Web3钱包加密模式...")
    
    # Web3钱包常见的密钥派生方式
    key_derivations = [
        ("Web3-PBKDF2-100000", lambda pwd, salt: PBKDF2(pwd, salt, 32, count=100000)),
        ("Web3-PBKDF2-4096", lambda pwd, salt: PBKDF2(pwd, salt, 32, count=4096)),
        ("Web3-Scrypt-Standard", lambda pwd, salt: scrypt(pwd.encode(), salt, 32, N=262144, r=8, p=1)),
        ("Web3-SHA256-Simple", lambda pwd, salt: hashlib.sha256(pwd.encode() + salt).digest()),
    ]
    
    # 尝试不同的数据结构
    for kdf_name, kdf_func in key_derivations:
        # 结构1: salt(32) + iv(16) + ciphertext
        if len(encrypted_data) >= 48:
            try:
                salt = encrypted_data[:32]
                iv = encrypted_data[32:48]
                ciphertext = encrypted_data[48:]
                
                key = kdf_func(password, salt)
                cipher = AES.new(key, AES.MODE_CBC, iv)
                decrypted = cipher.decrypt(ciphertext)
                
                try:
                    decrypted_unpadded = unpad(decrypted, AES.block_size)
                    results.append((f"{kdf_name}-32-16-CBC", decrypted_unpadded))
                except:
                    results.append((f"{kdf_name}-32-16-CBC-raw", decrypted))
            except:
                pass
        
        # 结构2: salt(16) + iv(16) + ciphertext  
        if len(encrypted_data) >= 32:
            try:
                salt = encrypted_data[:16]
                iv = encrypted_data[16:32]
                ciphertext = encrypted_data[32:]
                
                key = kdf_func(password, salt)
                cipher = AES.new(key, AES.MODE_CBC, iv)
                decrypted = cipher.decrypt(ciphertext)
                
                try:
                    decrypted_unpadded = unpad(decrypted, AES.block_size)
                    results.append((f"{kdf_name}-16-16-CBC", decrypted_unpadded))
                except:
                    results.append((f"{kdf_name}-16-16-CBC-raw", decrypted))
            except:
                pass
    
    return results

def analyze_for_json_wallet(data, method_name):
    """专门分析JSON格式的钱包数据"""
    try:
        # 尝试解析为JSON
        if data.startswith(b'{') or data.startswith(b'['):
            try:
                json_str = data.decode('utf-8')
                wallet_data = json.loads(json_str)
                
                print(f"\n✅ {method_name}: 成功解析JSON钱包数据!")
                print(f"JSON结构: {type(wallet_data)}")
                
                if isinstance(wallet_data, dict):
                    print(f"顶级键: {list(wallet_data.keys())}")
                    
                    # 搜索常见的钱包字段
                    wallet_fields = ['accounts', 'wallets', 'keys', 'addresses', 'privateKeys']
                    for field in wallet_fields:
                        if field in wallet_data:
                            print(f"找到钱包字段: {field}")
                            
                elif isinstance(wallet_data, list):
                    print(f"钱包数组长度: {len(wallet_data)}")
                    if len(wallet_data) > 0:
                        print(f"第一个元素类型: {type(wallet_data[0])}")
                        if isinstance(wallet_data[0], dict):
                            print(f"第一个元素键: {list(wallet_data[0].keys())}")
                
                # 搜索目标地址和私钥
                json_str_lower = json_str.lower()
                if TARGET_ADDRESS.lower() in json_str_lower:
                    print(f"🎯 在JSON中找到目标地址!")
                    
                    # 尝试提取对应的私钥
                    return extract_private_key_from_json(wallet_data, TARGET_ADDRESS)
                
                # 搜索所有可能的私钥
                import re
                hex_patterns = re.findall(r'[0-9a-fA-F]{64}', json_str)
                for pattern in hex_patterns:
                    address = private_key_to_address(pattern)
                    if address and address.lower() == TARGET_ADDRESS.lower():
                        print(f"🎯 找到目标私钥: {pattern}")
                        return True, {"private_key": pattern, "address": address, "method": method_name}
                
                return False, wallet_data
                
            except json.JSONDecodeError:
                pass
        
        # 如果不是JSON，尝试其他格式
        try:
            text = data.decode('utf-8')
            if TARGET_ADDRESS.lower() in text.lower():
                print(f"🎯 在文本中找到目标地址!")
                return True, text
        except:
            pass
        
        return False, None
        
    except Exception as e:
        print(f"分析JSON钱包数据时出错: {e}")
        return False, None

def extract_private_key_from_json(wallet_data, target_address):
    """从JSON钱包数据中提取私钥"""
    try:
        # 递归搜索JSON结构
        def search_json(obj, path=""):
            if isinstance(obj, dict):
                for key, value in obj.items():
                    current_path = f"{path}.{key}" if path else key
                    
                    # 检查是否为地址字段
                    if isinstance(value, str) and value.lower() == target_address.lower():
                        print(f"在路径 {current_path} 找到目标地址")
                        
                        # 查找对应的私钥字段
                        private_key_fields = ['privateKey', 'private_key', 'key', 'secret']
                        for pk_field in private_key_fields:
                            if pk_field in obj:
                                private_key = obj[pk_field]
                                if isinstance(private_key, str) and len(private_key) == 64:
                                    verify_address = private_key_to_address(private_key)
                                    if verify_address and verify_address.lower() == target_address.lower():
                                        return True, {"private_key": private_key, "address": verify_address}
                        
                        return True, {"found_address": value, "context": obj}
                    
                    # 递归搜索
                    result = search_json(value, current_path)
                    if result[0]:
                        return result
                        
            elif isinstance(obj, list):
                for i, item in enumerate(obj):
                    current_path = f"{path}[{i}]" if path else f"[{i}]"
                    result = search_json(item, current_path)
                    if result[0]:
                        return result
            
            return False, None
        
        return search_json(wallet_data)
        
    except Exception as e:
        print(f"提取私钥时出错: {e}")
        return False, None

def main():
    """主函数"""
    print("=" * 80)
    print("EpiK钱包智能定向攻击工具")
    print("专注于Flutter和Web3钱包的常见加密模式")
    print("=" * 80)
    
    # 提取加密数据
    encrypted_data = extract_encrypted_data()
    if not encrypted_data:
        print("错误: 无法提取加密数据")
        return
    
    print(f"加密数据长度: {len(encrypted_data)} 字节")
    print(f"数据开头: {binascii.hexlify(encrypted_data[:32]).decode()}")
    
    success_results = []
    
    # 对每个密码进行智能攻击
    for password in WALLET_PASSWORDS:
        print(f"\n{'='*60}")
        print(f"攻击密码: {password}")
        print(f"{'='*60}")
        
        # Flutter Keystore攻击
        flutter_results = try_flutter_keystore_decryption(encrypted_data, password)
        for method_name, decrypted_data in flutter_results:
            found_target, parsed_data = analyze_for_json_wallet(decrypted_data, method_name)
            if found_target:
                success_results.append({
                    "password": password,
                    "method": method_name,
                    "data": parsed_data
                })
        
        # Web3钱包攻击
        web3_results = try_web3_wallet_patterns(encrypted_data, password)
        for method_name, decrypted_data in web3_results:
            found_target, parsed_data = analyze_for_json_wallet(decrypted_data, method_name)
            if found_target:
                success_results.append({
                    "password": password,
                    "method": method_name,
                    "data": parsed_data
                })
    
    # 输出结果
    print(f"\n{'='*80}")
    if success_results:
        print("🎉 智能攻击成功!")
        for result in success_results:
            print(f"密码: {result['password']}")
            print(f"方法: {result['method']}")
            print(f"数据: {result['data']}")
        
        # 保存结果
        with open("smart_attack_results.json", "w") as f:
            json.dump(success_results, f, indent=2, ensure_ascii=False)
        print("结果已保存到: smart_attack_results.json")
    else:
        print("❌ 智能攻击失败")
        print("\n这表明EpiK可能使用了:")
        print("1. 非标准的加密实现")
        print("2. 硬件安全模块(HSM)")
        print("3. 需要额外认证因子的加密")
        print("4. 密码确实不正确")
        
        print(f"\n建议下一步:")
        print("1. 仔细确认钱包密码")
        print("2. 寻找专业的数字取证服务")
        print("3. 联系区块链钱包恢复专家")
    
    print(f"{'='*80}")

if __name__ == "__main__":
    main()
