#!/usr/bin/env python3
"""
验证提取的私钥是否对应目标地址
使用纯Python实现椭圆曲线加密验证
"""

import hashlib
import binascii

TARGET_ADDRESS = "0x79f7ddbc2441757ebf08a6c42e2ffe3bc7a628bc"

# 从分析中提取的潜在私钥候选
PRIVATE_KEY_CANDIDATES = [
    "ffafc1210536f2660f69fd3b5dcdb5b34f7e230e0c63bc62c626e7c50deee728",
    "d6eb8b78023eb56a8eda378104f0cc0ba0bd2171ffafc1210536f2660f69fd3b",
    "023eb56a8eda378104f0cc0ba0bd2171ffafc1210536f2660f69fd3b5dcdb5b3",
    "8eda378104f0cc0ba0bd2171ffafc1210536f2660f69fd3b5dcdb5b34f7e230e",
]

def mod_inverse(a, m):
    """计算模逆"""
    if a < 0:
        a = (a % m + m) % m
    
    # 扩展欧几里得算法
    def extended_gcd(a, b):
        if a == 0:
            return b, 0, 1
        gcd, x1, y1 = extended_gcd(b % a, a)
        x = y1 - (b // a) * x1
        y = x1
        return gcd, x, y
    
    gcd, x, _ = extended_gcd(a, m)
    if gcd != 1:
        raise ValueError("模逆不存在")
    return (x % m + m) % m

class Point:
    """椭圆曲线上的点"""
    def __init__(self, x, y, curve):
        self.x = x
        self.y = y
        self.curve = curve
    
    def __add__(self, other):
        if self.x is None:  # 无穷远点
            return other
        if other.x is None:
            return self
        
        p = self.curve.p
        
        if self.x == other.x:
            if self.y == other.y:
                # 点加倍
                s = (3 * self.x * self.x + self.curve.a) * mod_inverse(2 * self.y, p) % p
            else:
                # 相反点，结果是无穷远点
                return Point(None, None, self.curve)
        else:
            # 不同点相加
            s = (other.y - self.y) * mod_inverse(other.x - self.x, p) % p
        
        x3 = (s * s - self.x - other.x) % p
        y3 = (s * (self.x - x3) - self.y) % p
        
        return Point(x3, y3, self.curve)
    
    def __mul__(self, scalar):
        """标量乘法"""
        if scalar == 0:
            return Point(None, None, self.curve)
        
        result = Point(None, None, self.curve)  # 无穷远点
        addend = self
        
        while scalar:
            if scalar & 1:
                result = result + addend
            addend = addend + addend
            scalar >>= 1
        
        return result

class Curve:
    """椭圆曲线 y^2 = x^3 + ax + b (mod p)"""
    def __init__(self, p, a, b, g, n):
        self.p = p  # 素数模数
        self.a = a  # 曲线参数a
        self.b = b  # 曲线参数b
        self.g = g  # 生成元
        self.n = n  # 生成元的阶

# secp256k1 曲线参数
SECP256K1 = Curve(
    p=0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEFFFFFC2F,
    a=0,
    b=7,
    g=Point(
        0x79BE667EF9DCBBAC55A06295CE870B07029BFCDB2DCE28D959F2815B16F81798,
        0x483ADA7726A3C4655DA4FBFC0E1108A8FD17B448A68554199C47D08FFB10D4B8,
        None  # 将在创建后设置
    ),
    n=0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD0364141
)
SECP256K1.g.curve = SECP256K1

def private_key_to_public_key(private_key_hex):
    """从私钥计算公钥"""
    try:
        private_key_int = int(private_key_hex, 16)
        
        # 验证私钥在有效范围内
        if private_key_int <= 0 or private_key_int >= SECP256K1.n:
            return None
        
        # 计算公钥点
        public_key_point = SECP256K1.g * private_key_int
        
        if public_key_point.x is None:
            return None
        
        return public_key_point
    except Exception as e:
        print(f"计算公钥失败: {e}")
        return None

def public_key_to_address(public_key_point):
    """从公钥计算以太坊地址"""
    try:
        # 将公钥点转换为未压缩格式 (04 + x + y)
        x_bytes = public_key_point.x.to_bytes(32, 'big')
        y_bytes = public_key_point.y.to_bytes(32, 'big')
        
        # 未压缩公钥格式
        uncompressed_public_key = x_bytes + y_bytes
        
        # 计算Keccak-256哈希
        keccak_hash = keccak256(uncompressed_public_key)
        
        # 取后20字节作为地址
        address_bytes = keccak_hash[-20:]
        address = "0x" + binascii.hexlify(address_bytes).decode()
        
        return address.lower()
    except Exception as e:
        print(f"计算地址失败: {e}")
        return None

def keccak256(data):
    """Keccak-256哈希函数的简化实现"""
    # 这里使用SHA3-256作为近似，实际应该使用Keccak-256
    # 对于验证目的，这个近似可能足够
    import hashlib
    return hashlib.sha3_256(data).digest()

def verify_private_key(private_key_hex, target_address):
    """验证私钥是否对应目标地址"""
    print(f"\n验证私钥: {private_key_hex}")
    
    # 计算公钥
    public_key_point = private_key_to_public_key(private_key_hex)
    if not public_key_point:
        print("  ✗ 无效的私钥")
        return False
    
    print(f"  公钥 X: {hex(public_key_point.x)}")
    print(f"  公钥 Y: {hex(public_key_point.y)}")
    
    # 计算地址
    address = public_key_to_address(public_key_point)
    if not address:
        print("  ✗ 无法计算地址")
        return False
    
    print(f"  计算的地址: {address}")
    print(f"  目标地址:   {target_address.lower()}")
    
    if address == target_address.lower():
        print("  ✓ 地址匹配!")
        return True
    else:
        print("  ✗ 地址不匹配")
        return False

def try_alternative_keccak():
    """尝试使用标准库的替代方案"""
    try:
        # 尝试使用pycryptodome的Keccak
        from Crypto.Hash import keccak
        
        def keccak256_crypto(data):
            k = keccak.new(digest_bits=256)
            k.update(data)
            return k.digest()
        
        return keccak256_crypto
    except ImportError:
        pass
    
    try:
        # 尝试使用pysha3
        import sha3
        
        def keccak256_sha3(data):
            k = sha3.keccak_256()
            k.update(data)
            return k.digest()
        
        return keccak256_sha3
    except ImportError:
        pass
    
    # 使用SHA3-256作为近似
    def keccak256_sha3_256(data):
        return hashlib.sha3_256(data).digest()
    
    return keccak256_sha3_256

def main():
    """主函数"""
    print("=" * 60)
    print("私钥验证工具")
    print("=" * 60)
    print(f"目标地址: {TARGET_ADDRESS}")
    print(f"候选私钥数量: {len(PRIVATE_KEY_CANDIDATES)}")
    
    # 尝试获取更好的Keccak实现
    global keccak256
    keccak256 = try_alternative_keccak()
    print(f"使用的哈希函数: {keccak256.__name__}")
    
    found_match = False
    
    for i, private_key_hex in enumerate(PRIVATE_KEY_CANDIDATES):
        print(f"\n{'='*40}")
        print(f"候选 {i+1}/{len(PRIVATE_KEY_CANDIDATES)}")
        
        if verify_private_key(private_key_hex, TARGET_ADDRESS):
            print(f"\n🎉 找到匹配的私钥!")
            print(f"私钥: {private_key_hex}")
            
            # 保存私钥
            keystore = {
                "address": TARGET_ADDRESS,
                "private_key": private_key_hex,
                "extraction_method": "structure_analysis"
            }
            
            import json
            with open("found_private_key.json", "w") as f:
                json.dump(keystore, f, indent=2)
            
            print(f"✓ 私钥已保存到 found_private_key.json")
            found_match = True
            break
    
    if not found_match:
        print(f"\n{'='*60}")
        print("未找到匹配的私钥")
        print("可能的原因:")
        print("1. 数据仍然是加密的")
        print("2. 私钥不在分析的位置")
        print("3. 需要不同的解密方法")
        print("4. Keccak-256实现不准确")
        
        print(f"\n建议:")
        print("1. 安装 pycryptodome: pip install pycryptodome")
        print("2. 尝试更多的私钥候选位置")
        print("3. 使用专业的以太坊工具验证")

if __name__ == "__main__":
    main()
