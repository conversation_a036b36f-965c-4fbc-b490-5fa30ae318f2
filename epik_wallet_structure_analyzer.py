#!/usr/bin/env python3
"""
EpiK Portal 钱包结构分析器
基于Flutter应用的钱包数据结构进行分析
"""

import base64
import binascii
import json
import xml.etree.ElementTree as ET
import hashlib
import struct
from pathlib import Path

TARGET_ADDRESS = "0x79f7ddbc2441757ebf08a6c42e2ffe3bc7a628bc"
KNOWN_PASSWORDS = ["zyuu23521", "123456", "password"]

def extract_all_flutter_data():
    """提取所有Flutter相关数据"""
    print("=== 提取Flutter数据 ===")
    
    prefs_file = "EpiK_Portal_Restore_Package/app_data/shared_prefs/FlutterSharedPreferences.xml"
    tree = ET.parse(prefs_file)
    root = tree.getroot()
    
    flutter_data = {}
    
    for string_elem in root.findall('string'):
        name = string_elem.get('name')
        value = string_elem.text or ""
        
        if name.startswith('flutter.'):
            flutter_data[name] = value
            print(f"  {name}: {len(value)} 字符")
    
    return flutter_data

def analyze_account_list_structure(encrypted_data):
    """分析账户列表的可能结构"""
    print("\n=== 分析账户列表结构 ===")
    
    # 912字节可能的结构分析
    print(f"总长度: {len(encrypted_data)} 字节")
    
    # 尝试不同的结构假设
    structures = [
        ("单个账户", 912),
        ("两个账户", 456),
        ("三个账户", 304),
        ("四个账户", 228),
        ("头部+数据", [16, 896]),  # 16字节头部
        ("头部+数据", [32, 880]),  # 32字节头部
        ("头部+数据", [64, 848]),  # 64字节头部
    ]
    
    for desc, size_info in structures:
        print(f"\n假设: {desc}")
        
        if isinstance(size_info, int):
            # 单一大小
            print(f"  每个账户: {size_info} 字节")
            analyze_single_account_structure(encrypted_data[:size_info])
        else:
            # 头部+数据结构
            header_size, data_size = size_info
            header = encrypted_data[:header_size]
            data = encrypted_data[header_size:]
            
            print(f"  头部: {header_size} 字节")
            print(f"  数据: {data_size} 字节")
            print(f"  头部十六进制: {binascii.hexlify(header).decode()}")
            
            # 分析头部是否包含长度信息
            analyze_header(header)

def analyze_header(header):
    """分析头部数据"""
    print(f"    头部分析:")
    
    # 尝试解析为不同的数据类型
    if len(header) >= 4:
        # 32位整数
        int32_le = struct.unpack('<I', header[:4])[0]
        int32_be = struct.unpack('>I', header[:4])[0]
        print(f"      前4字节作为int32: LE={int32_le}, BE={int32_be}")
    
    if len(header) >= 8:
        # 64位整数
        int64_le = struct.unpack('<Q', header[:8])[0]
        int64_be = struct.unpack('>Q', header[:8])[0]
        print(f"      前8字节作为int64: LE={int64_le}, BE={int64_be}")

def analyze_single_account_structure(account_data):
    """分析单个账户的可能结构"""
    print(f"    账户数据分析 ({len(account_data)} 字节):")
    
    # 常见的钱包数据结构
    possible_structures = [
        ("地址(20) + 私钥(32) + 其他", [20, 32]),
        ("私钥(32) + 地址(20) + 其他", [32, 20]),
        ("长度(4) + 私钥(32) + 地址(20) + 其他", [4, 32, 20]),
        ("魔数(4) + 版本(4) + 私钥(32) + 地址(20)", [4, 4, 32, 20]),
    ]
    
    for desc, sizes in possible_structures:
        print(f"      假设结构: {desc}")
        offset = 0
        for i, size in enumerate(sizes):
            if offset + size <= len(account_data):
                chunk = account_data[offset:offset + size]
                hex_chunk = binascii.hexlify(chunk).decode()
                print(f"        部分{i+1} ({size}字节): {hex_chunk}")
                
                # 检查是否可能是地址
                if size == 20:
                    address_hex = "0x" + hex_chunk
                    if address_hex.lower() == TARGET_ADDRESS.lower():
                        print(f"        ✓ 找到目标地址!")
                
                # 检查是否可能是私钥
                if size == 32:
                    if is_potential_private_key(chunk):
                        print(f"        ✓ 可能的私钥: {hex_chunk}")
                
                offset += size
            else:
                break

def is_potential_private_key(key_bytes):
    """检查是否可能是私钥"""
    if len(key_bytes) != 32:
        return False
    
    # 不应该全为0或全为FF
    if key_bytes == b'\x00' * 32 or key_bytes == b'\xff' * 32:
        return False
    
    # 应该有一定的随机性
    unique_bytes = len(set(key_bytes))
    if unique_bytes < 8:
        return False
    
    return True

def try_flutter_specific_decryption(encrypted_data, password):
    """尝试Flutter特定的解密方法"""
    print(f"\n=== Flutter解密尝试: {password} ===")
    
    # Flutter通常使用的加密方法
    methods = [
        ("Flutter AES-GCM", try_flutter_aes_gcm),
        ("Flutter AES-CBC", try_flutter_aes_cbc),
        ("简单密码派生", try_simple_key_derivation),
    ]
    
    for method_name, method_func in methods:
        try:
            result = method_func(encrypted_data, password)
            if result:
                print(f"✓ {method_name} 成功!")
                return result
        except Exception as e:
            print(f"✗ {method_name} 失败: {e}")
    
    return None

def try_flutter_aes_gcm(encrypted_data, password):
    """尝试Flutter AES-GCM解密"""
    # Flutter通常使用前12字节作为nonce，后16字节作为tag
    if len(encrypted_data) < 28:
        return None
    
    # 尝试不同的结构
    structures = [
        (12, 16),  # 12字节nonce + 16字节tag
        (16, 16),  # 16字节IV + 16字节tag
    ]
    
    for nonce_len, tag_len in structures:
        if len(encrypted_data) < nonce_len + tag_len:
            continue
        
        nonce = encrypted_data[:nonce_len]
        tag = encrypted_data[-tag_len:]
        ciphertext = encrypted_data[nonce_len:-tag_len]
        
        # 使用密码派生密钥
        key = hashlib.pbkdf2_hmac('sha256', password.encode(), b'flutter_salt', 10000, 32)
        
        print(f"    尝试结构: nonce={nonce_len}, tag={tag_len}, cipher={len(ciphertext)}")
        
        # 这里需要实际的AES-GCM实现，暂时跳过
        # 在实际使用中需要安装cryptography库
    
    return None

def try_flutter_aes_cbc(encrypted_data, password):
    """尝试Flutter AES-CBC解密"""
    if len(encrypted_data) < 32:
        return None
    
    # 假设前16字节是IV
    iv = encrypted_data[:16]
    ciphertext = encrypted_data[16:]
    
    # 使用密码派生密钥
    key = hashlib.pbkdf2_hmac('sha256', password.encode(), b'flutter_salt', 10000, 32)
    
    print(f"    IV: {binascii.hexlify(iv).decode()}")
    print(f"    密文长度: {len(ciphertext)}")
    
    # 这里需要实际的AES-CBC实现
    return None

def try_simple_key_derivation(encrypted_data, password):
    """尝试简单的密钥派生"""
    # 使用密码的不同哈希作为密钥进行XOR
    hash_methods = [
        hashlib.md5(password.encode()).digest(),
        hashlib.sha1(password.encode()).digest(),
        hashlib.sha256(password.encode()).digest(),
        hashlib.sha512(password.encode()).digest()[:32],
    ]
    
    for i, key in enumerate(hash_methods):
        result = bytearray()
        for j, byte in enumerate(encrypted_data):
            result.append(byte ^ key[j % len(key)])
        
        # 检查结果是否包含可识别的模式
        result_hex = binascii.hexlify(result).decode().lower()
        target_hex = TARGET_ADDRESS.lower().replace('0x', '')
        
        if target_hex in result_hex:
            print(f"    ✓ 哈希方法 {i} 找到目标地址!")
            return result
        
        # 检查是否包含JSON模式
        try:
            result_str = result.decode('utf-8', errors='ignore')
            if '{' in result_str and '"' in result_str:
                print(f"    可能的JSON结构 (方法 {i}): {result_str[:100]}...")
        except:
            pass
    
    return None

def search_for_keystore_json(data):
    """搜索keystore JSON结构"""
    print("\n=== 搜索Keystore JSON ===")
    
    # 尝试在数据中找到JSON结构的开始
    for i in range(len(data) - 10):
        # 寻找可能的JSON开始
        if data[i] == ord('{'):
            # 尝试提取JSON
            for end in range(i + 10, min(len(data), i + 500)):
                try:
                    candidate = data[i:end].decode('utf-8', errors='ignore')
                    if candidate.count('{') == candidate.count('}') and candidate.endswith('}'):
                        try:
                            parsed = json.loads(candidate)
                            print(f"✓ 找到JSON结构 (位置 {i}): {candidate}")
                            return parsed
                        except:
                            pass
                except:
                    pass
    
    print("未找到有效的JSON结构")
    return None

def main():
    """主函数"""
    print("=" * 60)
    print("EpiK Portal 钱包结构分析器")
    print("=" * 60)
    
    # 提取所有Flutter数据
    flutter_data = extract_all_flutter_data()
    
    # 获取加密的账户数据
    encrypted_account_data = base64.b64decode(flutter_data.get('flutter.am_account_list', ''))
    
    if not encrypted_account_data:
        print("未找到加密的账户数据")
        return
    
    print(f"\n加密账户数据: {len(encrypted_account_data)} 字节")
    
    # 分析数据结构
    analyze_account_list_structure(encrypted_account_data)
    
    # 尝试解密
    for password in KNOWN_PASSWORDS:
        result = try_flutter_specific_decryption(encrypted_account_data, password)
        if result:
            print(f"\n🎉 解密成功! 密码: {password}")
            
            # 保存结果
            with open("flutter_decrypted_data.bin", "wb") as f:
                f.write(result)
            
            # 尝试解析为JSON
            try:
                json_str = result.decode('utf-8', errors='ignore')
                parsed = json.loads(json_str)
                print(f"解析的JSON: {json.dumps(parsed, indent=2)}")
            except:
                print(f"解密数据 (十六进制): {binascii.hexlify(result[:100]).decode()}...")
            
            return
    
    # 搜索可能的keystore结构
    search_for_keystore_json(encrypted_account_data)
    
    print("\n" + "=" * 60)
    print("分析完成")
    print("建议:")
    print("1. 尝试更多密码组合")
    print("2. 分析Flutter应用的加密实现")
    print("3. 考虑使用专业的加密分析工具")

if __name__ == "__main__":
    main()
