# 小米备份恢复详细指南

## 📋 概述
本指南详细说明如何使用小米自带的备份工具恢复EpiK Portal钱包应用。

## 🔧 准备工作

### 1. 生成小米备份格式
在电脑上运行：
```bash
cd EpiK_Portal_Restore_Package
./create_miui_backup.sh
```

执行成功后会生成 `miui_backup` 目录，包含：
```
miui_backup/
└── apps/
    └── com.epik.wallet/
        ├── _manifest          # 应用清单文件
        ├── base.apk          # 应用安装包
        ├── shared_prefs/     # 配置文件
        ├── databases/        # 数据库文件
        ├── files/           # 应用文件
        └── app_webview/     # WebView数据
```

## 📱 传输备份文件到新手机

### 方法一：ADB传输（推荐）

#### 前提条件
- 新手机开启USB调试
- 电脑安装ADB工具

#### 操作步骤
1. 连接新手机到电脑
2. 确认ADB连接：
   ```bash
   adb devices
   ```
3. 创建备份目录：
   ```bash
   adb shell mkdir -p /sdcard/MIUI/backup/AllBackup
   ```
4. 传输备份文件：
   ```bash
   adb push miui_backup /sdcard/MIUI/backup/AllBackup/EpiK_Portal_Backup
   ```

### 方法二：手动传输

#### 使用数据线传输
1. 连接新手机到电脑（选择文件传输模式）
2. 在电脑上打开手机存储
3. 导航到：`内部存储/MIUI/backup/AllBackup/`
4. 将 `miui_backup` 目录复制到此位置
5. 重命名为 `EpiK_Portal_Backup`

#### 使用云盘传输
1. 将 `miui_backup` 目录上传到云盘（如小米云盘、百度网盘等）
2. 在新手机上下载到：`/sdcard/MIUI/backup/AllBackup/EpiK_Portal_Backup`

#### 使用U盘传输
1. 将 `miui_backup` 目录复制到U盘
2. 使用OTG线连接U盘到新手机
3. 使用文件管理器将目录复制到：`/sdcard/MIUI/backup/AllBackup/EpiK_Portal_Backup`

## 🔄 在新手机上恢复备份

### 步骤一：打开小米备份工具
1. 打开 **设置** 应用
2. 滑动到底部，找到 **更多设置**
3. 点击 **备份与重置**
4. 选择 **本地备份**

### 步骤二：选择恢复
1. 在备份界面点击 **恢复**
2. 系统会扫描备份文件
3. 找到 **EpiK_Portal_Backup** 备份
4. 点击进入备份详情

### 步骤三：选择恢复内容
1. 在备份内容列表中找到 **EpiK Portal**
2. 确保勾选该应用
3. 点击 **开始恢复**

### 步骤四：等待恢复完成
1. 系统会自动安装APK并恢复数据
2. 恢复过程可能需要几分钟
3. 完成后会显示恢复成功提示

### 步骤五：验证恢复
1. 重启手机（推荐）
2. 打开EpiK Portal应用
3. 使用密码 `zyuu23521` 解锁
4. 验证钱包地址：`0x79f7ddbc2441757ebf08a6c42e2ffe3bc7a628bc`

## 🚨 故障排除

### 问题1：找不到备份文件
**可能原因**：
- 文件路径不正确
- 权限问题

**解决方案**：
1. 确认文件在正确路径：`/sdcard/MIUI/backup/AllBackup/`
2. 检查文件权限：
   ```bash
   adb shell ls -la /sdcard/MIUI/backup/AllBackup/
   ```
3. 尝试重新传输文件

### 问题2：备份文件损坏
**症状**：
- 恢复过程中出错
- 应用无法正常启动

**解决方案**：
1. 重新生成备份文件
2. 检查原始数据完整性
3. 尝试其他恢复方法

### 问题3：应用恢复后无数据
**可能原因**：
- 数据文件未正确包含
- 权限设置问题

**解决方案**：
1. 检查 `_manifest` 文件内容
2. 确认数据文件完整性
3. 手动设置应用权限

### 问题4：小米备份工具无法识别
**可能原因**：
- 备份格式不正确
- MIUI版本不兼容

**解决方案**：
1. 检查MIUI版本（建议MIUI 12+）
2. 尝试使用其他恢复方法
3. 更新小米备份工具

## 📂 备份目录结构说明

### 标准小米备份路径
```
/sdcard/MIUI/backup/AllBackup/
├── 备份名称1/
├── 备份名称2/
└── EpiK_Portal_Backup/    # 我们的备份
    └── apps/
        └── com.epik.wallet/
            ├── _manifest
            ├── base.apk
            └── [数据文件]
```

### 备份清单文件 (_manifest)
```
1                    # 版本号
com.epik.wallet     # 包名
118                 # 版本代码
30                  # 目标SDK版本

1                   # 包含APK
1                   # 包含数据
```

## ⚙️ 高级选项

### 自定义备份名称
如果需要自定义备份名称，可以修改脚本：
```bash
# 编辑 create_miui_backup.sh
BACKUP_NAME="EpiK_Portal_$(date +%Y%m%d)"
mkdir -p miui_backup_$BACKUP_NAME
```

### 批量恢复多个应用
如果有多个应用需要恢复：
1. 将所有应用备份放在同一个备份目录下
2. 小米备份工具会显示所有可恢复的应用
3. 可以选择性恢复需要的应用

## 📞 技术支持

### 验证备份完整性
```bash
# 检查备份文件大小
du -sh miui_backup/

# 验证关键文件
ls -la miui_backup/apps/com.epik.wallet/shared_prefs/
```

### 日志查看
如果恢复失败，可以查看系统日志：
```bash
adb logcat | grep -i backup
adb logcat | grep -i epik
```

---

## 📝 总结

使用小米备份恢复的优势：
- ✅ 无需root权限
- ✅ 系统原生支持
- ✅ 操作简单直观
- ✅ 自动处理权限

按照以上步骤，您应该能够成功在新的小米手机上恢复EpiK Portal钱包应用和所有数据。
