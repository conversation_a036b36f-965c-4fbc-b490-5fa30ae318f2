#!/usr/bin/env python3
"""
EpiK Portal 高级分析工具
深度分析912字节加密数据，寻找私钥和keystore
"""

import base64
import binascii
import json
import xml.etree.ElementTree as ET
import hashlib
import struct
from pathlib import Path
import re

TARGET_ADDRESS = "0x79f7ddbc2441757ebf08a6c42e2ffe3bc7a628bc"
KNOWN_PASSWORDS = ["zyuu23521", "123456", "password"]

def extract_encrypted_data():
    """提取912字节的加密数据"""
    prefs_file = "EpiK_Portal_Restore_Package/app_data/shared_prefs/FlutterSharedPreferences.xml"
    
    tree = ET.parse(prefs_file)
    root = tree.getroot()
    
    for string_elem in root.findall('string'):
        name = string_elem.get('name')
        if name == 'flutter.am_account_list':
            encrypted_b64 = string_elem.text
            encrypted_data = base64.b64decode(encrypted_b64)
            return encrypted_data
    return None

def analyze_data_structure(data):
    """分析数据结构"""
    print("=== 数据结构分析 ===")
    print(f"总长度: {len(data)} 字节")
    
    # 分析前64字节
    print(f"\n前64字节 (十六进制):")
    hex_preview = binascii.hexlify(data[:64]).decode()
    for i in range(0, len(hex_preview), 32):
        print(f"  {i//2:04x}: {hex_preview[i:i+32]}")
    
    # 分析后64字节
    print(f"\n后64字节 (十六进制):")
    hex_preview = binascii.hexlify(data[-64:]).decode()
    for i in range(0, len(hex_preview), 32):
        print(f"  {(len(data)-64+i//2):04x}: {hex_preview[i:i+32]}")
    
    # 查找重复模式
    print(f"\n重复模式分析:")
    patterns = {}
    for i in range(len(data) - 3):
        pattern = data[i:i+4]
        if pattern in patterns:
            patterns[pattern].append(i)
        else:
            patterns[pattern] = [i]
    
    repeated = {k: v for k, v in patterns.items() if len(v) > 1}
    print(f"找到 {len(repeated)} 个重复的4字节模式")
    
    # 熵分析
    byte_counts = [0] * 256
    for byte in data:
        byte_counts[byte] += 1

    import math
    entropy = 0
    for count in byte_counts:
        if count > 0:
            p = count / len(data)
            entropy -= p * math.log2(p)

    print(f"数据熵: {entropy:.2f} (0=完全有序, 8=完全随机)")

def search_for_private_keys(data):
    """搜索可能的私钥"""
    print("\n=== 搜索私钥模式 ===")
    
    candidates = []
    
    # 搜索32字节序列
    for i in range(len(data) - 31):
        candidate = data[i:i+32]
        
        # 检查是否可能是私钥
        if is_potential_private_key(candidate):
            hex_key = binascii.hexlify(candidate).decode()
            candidates.append((i, hex_key))
    
    print(f"找到 {len(candidates)} 个潜在的32字节私钥候选")
    
    # 显示前10个候选
    for i, (offset, hex_key) in enumerate(candidates[:10]):
        print(f"  候选 {i+1}: 偏移 {offset:03x} - {hex_key}")
    
    return candidates

def is_potential_private_key(key_bytes):
    """检查是否可能是私钥"""
    # 私钥不应该全为0或全为FF
    if key_bytes == b'\x00' * 32 or key_bytes == b'\xff' * 32:
        return False
    
    # 私钥应该有一定的随机性
    unique_bytes = len(set(key_bytes))
    if unique_bytes < 8:  # 至少8个不同的字节值
        return False
    
    # 检查是否在有效的椭圆曲线范围内（简化检查）
    # secp256k1的模数是一个很大的数，这里简化检查
    key_int = int.from_bytes(key_bytes, 'big')
    if key_int == 0 or key_int >= 2**256:
        return False
    
    return True

def search_for_json_structures(data):
    """搜索JSON结构"""
    print("\n=== 搜索JSON结构 ===")
    
    # 尝试在不同位置查找JSON开始标记
    json_markers = [b'{', b'[', b'"']
    
    for marker in json_markers:
        positions = []
        start = 0
        while True:
            pos = data.find(marker, start)
            if pos == -1:
                break
            positions.append(pos)
            start = pos + 1
        
        if positions:
            print(f"找到 {len(positions)} 个 '{marker.decode()}' 标记，位置: {positions[:10]}")

def search_for_address_patterns(data):
    """搜索地址模式"""
    print("\n=== 搜索地址模式 ===")
    
    target_hex = TARGET_ADDRESS.lower().replace('0x', '')
    
    # 在原始数据中搜索
    hex_data = binascii.hexlify(data).decode().lower()
    
    # 搜索完整地址
    if target_hex in hex_data:
        pos = hex_data.find(target_hex)
        byte_pos = pos // 2
        print(f"✓ 找到完整地址，位置: {byte_pos} (0x{byte_pos:x})")
        
        # 显示周围的数据
        start = max(0, byte_pos - 32)
        end = min(len(data), byte_pos + 32)
        context = binascii.hexlify(data[start:end]).decode()
        print(f"  上下文: {context}")
    
    # 搜索地址片段
    for i in range(0, len(target_hex), 8):
        fragment = target_hex[i:i+8]
        if len(fragment) >= 6 and fragment in hex_data:
            pos = hex_data.find(fragment)
            byte_pos = pos // 2
            print(f"找到地址片段 '{fragment}'，位置: {byte_pos} (0x{byte_pos:x})")

def try_different_encodings(data):
    """尝试不同的编码方式"""
    print("\n=== 尝试不同编码 ===")
    
    encodings = ['utf-8', 'utf-16', 'utf-32', 'ascii', 'latin1']
    
    for encoding in encodings:
        try:
            decoded = data.decode(encoding, errors='ignore')
            if TARGET_ADDRESS.lower() in decoded.lower():
                print(f"✓ {encoding} 编码中找到目标地址!")
                print(f"  内容预览: {decoded[:200]}...")
                return decoded
        except:
            pass
    
    return None

def analyze_potential_keystore_structure(data):
    """分析可能的keystore结构"""
    print("\n=== 分析Keystore结构 ===")
    
    # 常见的keystore字段
    keystore_fields = [
        b'address', b'crypto', b'cipher', b'ciphertext', 
        b'kdf', b'mac', b'version', b'id', b'private'
    ]
    
    found_fields = []
    for field in keystore_fields:
        if field in data:
            positions = []
            start = 0
            while True:
                pos = data.find(field, start)
                if pos == -1:
                    break
                positions.append(pos)
                start = pos + 1
            
            if positions:
                found_fields.append((field.decode(), positions))
                print(f"找到字段 '{field.decode()}'，位置: {positions}")
    
    if found_fields:
        print(f"总共找到 {len(found_fields)} 个keystore相关字段")
    else:
        print("未找到标准keystore字段")

def extract_32_byte_sequences(data):
    """提取所有32字节序列并分析"""
    print("\n=== 提取32字节序列 ===")
    
    sequences = []
    for i in range(len(data) - 31):
        seq = data[i:i+32]
        hex_seq = binascii.hexlify(seq).decode()
        sequences.append((i, hex_seq))
    
    print(f"总共 {len(sequences)} 个32字节序列")
    
    # 保存到文件供进一步分析
    with open("all_32byte_sequences.txt", "w") as f:
        f.write("偏移量,十六进制序列\n")
        for offset, hex_seq in sequences:
            f.write(f"{offset:03x},{hex_seq}\n")
    
    print("✓ 所有32字节序列已保存到 all_32byte_sequences.txt")
    
    return sequences

def main():
    """主函数"""
    print("=" * 60)
    print("EpiK Portal 高级数据分析工具")
    print("=" * 60)
    print(f"目标地址: {TARGET_ADDRESS}")
    print()
    
    # 提取加密数据
    encrypted_data = extract_encrypted_data()
    if not encrypted_data:
        print("无法提取加密数据")
        return
    
    # 保存原始数据
    with open("encrypted_data_912bytes.bin", "wb") as f:
        f.write(encrypted_data)
    print("✓ 原始加密数据已保存到 encrypted_data_912bytes.bin")
    
    # 各种分析
    analyze_data_structure(encrypted_data)
    search_for_address_patterns(encrypted_data)
    search_for_private_keys(encrypted_data)
    search_for_json_structures(encrypted_data)
    analyze_potential_keystore_structure(encrypted_data)
    try_different_encodings(encrypted_data)
    extract_32_byte_sequences(encrypted_data)
    
    print("\n" + "=" * 60)
    print("分析完成！")
    print("生成的文件:")
    print("- encrypted_data_912bytes.bin (原始加密数据)")
    print("- all_32byte_sequences.txt (所有32字节序列)")
    print("\n建议下一步:")
    print("1. 检查 all_32byte_sequences.txt 中的序列")
    print("2. 尝试使用专业的加密分析工具")
    print("3. 考虑联系EpiK Protocol技术支持")

if __name__ == "__main__":
    main()
