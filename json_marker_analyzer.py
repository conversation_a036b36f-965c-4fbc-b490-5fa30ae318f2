#!/usr/bin/env python3
"""
分析JSON标记位置，尝试提取可能的钱包数据
"""

import base64
import binascii
import json
import xml.etree.ElementTree as ET
from pathlib import Path

def extract_encrypted_data():
    """提取加密数据"""
    prefs_file = "EpiK_Portal_Restore_Package/app_data/shared_prefs/FlutterSharedPreferences.xml"
    tree = ET.parse(prefs_file)
    root = tree.getroot()
    
    for string_elem in root.findall('string'):
        name = string_elem.get('name')
        if name == 'flutter.am_account_list':
            encrypted_b64 = string_elem.text
            encrypted_data = base64.b64decode(encrypted_b64)
            return encrypted_data
    return None

def analyze_json_markers(data):
    """分析JSON标记位置"""
    print("=== 分析JSON标记位置 ===")
    
    # 找到的JSON标记位置
    brace_positions = [545, 809, 847, 848, 871]  # '{' 位置
    bracket_positions = [55]  # '[' 位置
    quote_positions = [68]  # '"' 位置
    
    print("分析 '{' 标记位置:")
    for pos in brace_positions:
        print(f"\n位置 {pos} (0x{pos:x}):")
        
        # 显示周围的数据
        start = max(0, pos - 20)
        end = min(len(data), pos + 50)
        context = data[start:end]
        
        print(f"  十六进制: {binascii.hexlify(context).decode()}")
        
        # 尝试作为文本解码
        try:
            text = context.decode('utf-8', errors='ignore')
            print(f"  文本: {repr(text)}")
        except:
            pass
        
        # 尝试从这个位置开始解析JSON
        try_parse_json_from_position(data, pos)

def try_parse_json_from_position(data, start_pos):
    """尝试从指定位置解析JSON"""
    print(f"  尝试从位置 {start_pos} 解析JSON:")
    
    # 尝试不同长度的数据
    for length in [50, 100, 200, 300, 500]:
        if start_pos + length > len(data):
            continue
        
        chunk = data[start_pos:start_pos + length]
        
        # 尝试直接解析
        try:
            text = chunk.decode('utf-8', errors='ignore')
            if '{' in text and '}' in text:
                # 找到第一个完整的JSON对象
                start_brace = text.find('{')
                brace_count = 0
                end_pos = start_brace
                
                for i, char in enumerate(text[start_brace:], start_brace):
                    if char == '{':
                        brace_count += 1
                    elif char == '}':
                        brace_count -= 1
                        if brace_count == 0:
                            end_pos = i + 1
                            break
                
                if brace_count == 0:
                    json_text = text[start_brace:end_pos]
                    try:
                        parsed = json.loads(json_text)
                        print(f"    ✓ 成功解析JSON (长度 {length}): {json_text}")
                        return parsed
                    except:
                        pass
        except:
            pass
    
    print(f"    ✗ 无法解析JSON")
    return None

def search_for_wallet_patterns(data):
    """搜索钱包相关模式"""
    print("\n=== 搜索钱包模式 ===")
    
    # 常见的钱包相关字符串
    wallet_patterns = [
        b'private', b'key', b'address', b'mnemonic', b'seed',
        b'wallet', b'account', b'crypto', b'cipher', b'password',
        b'******************************************',
        b'79f7ddbc2441757ebf08a6c42e2ffe3bc7a628bc'
    ]
    
    for pattern in wallet_patterns:
        positions = []
        start = 0
        while True:
            pos = data.find(pattern, start)
            if pos == -1:
                break
            positions.append(pos)
            start = pos + 1
        
        if positions:
            print(f"找到模式 '{pattern.decode(errors='ignore')}'，位置: {positions}")
            
            # 显示每个位置的上下文
            for pos in positions[:3]:  # 只显示前3个
                start = max(0, pos - 30)
                end = min(len(data), pos + 50)
                context = data[start:end]
                print(f"  位置 {pos}: {binascii.hexlify(context).decode()}")

def extract_potential_json_objects(data):
    """提取可能的JSON对象"""
    print("\n=== 提取可能的JSON对象 ===")
    
    # 在所有 '{' 位置尝试提取JSON
    brace_positions = []
    for i, byte in enumerate(data):
        if byte == ord('{'):
            brace_positions.append(i)
    
    print(f"找到 {len(brace_positions)} 个 '{{' 位置: {brace_positions}")
    
    extracted_objects = []
    
    for pos in brace_positions:
        # 尝试提取从这个位置开始的JSON对象
        for max_len in [100, 200, 500, 1000]:
            if pos + max_len > len(data):
                max_len = len(data) - pos
            
            chunk = data[pos:pos + max_len]
            
            try:
                # 尝试解码为文本
                text = chunk.decode('utf-8', errors='replace')
                
                # 找到匹配的大括号
                brace_count = 0
                end_pos = 0
                
                for i, char in enumerate(text):
                    if char == '{':
                        brace_count += 1
                    elif char == '}':
                        brace_count -= 1
                        if brace_count == 0:
                            end_pos = i + 1
                            break
                
                if brace_count == 0 and end_pos > 0:
                    json_candidate = text[:end_pos]
                    try:
                        parsed = json.loads(json_candidate)
                        print(f"✓ 位置 {pos} 提取到JSON: {json_candidate}")
                        extracted_objects.append((pos, parsed))
                        break
                    except json.JSONDecodeError:
                        pass
            except:
                pass
    
    return extracted_objects

def analyze_data_around_markers(data):
    """分析标记周围的数据"""
    print("\n=== 分析标记周围数据 ===")
    
    interesting_positions = [55, 68, 545, 809, 847, 848, 871]
    
    for pos in interesting_positions:
        print(f"\n位置 {pos} (0x{pos:x}) 周围的数据:")
        
        # 提取周围64字节的数据
        start = max(0, pos - 32)
        end = min(len(data), pos + 32)
        context = data[start:end]
        
        # 十六进制显示
        hex_data = binascii.hexlify(context).decode()
        print(f"  十六进制: {hex_data}")
        
        # 尝试不同的解码方式
        encodings = ['utf-8', 'ascii', 'latin1']
        for encoding in encodings:
            try:
                text = context.decode(encoding, errors='ignore')
                if any(c.isprintable() for c in text):
                    print(f"  {encoding}: {repr(text)}")
                    break
            except:
                pass

def main():
    """主函数"""
    print("=" * 60)
    print("JSON标记分析工具")
    print("=" * 60)
    
    # 提取加密数据
    encrypted_data = extract_encrypted_data()
    if not encrypted_data:
        print("无法提取加密数据")
        return
    
    print(f"数据长度: {len(encrypted_data)} 字节")
    
    # 各种分析
    analyze_json_markers(encrypted_data)
    search_for_wallet_patterns(encrypted_data)
    extract_potential_json_objects(encrypted_data)
    analyze_data_around_markers(encrypted_data)
    
    print("\n" + "=" * 60)
    print("分析完成")

if __name__ == "__main__":
    main()
