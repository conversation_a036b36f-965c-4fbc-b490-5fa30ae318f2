#!/usr/bin/env python3
"""
直接钱包文件搜索工具
在解包的应用数据中直接搜索钱包文件、私钥存储等
"""

import base64
import json
import xml.etree.ElementTree as ET
import hashlib
import binascii
import re
import os
import sqlite3
from pathlib import Path

TARGET_ADDRESS = "0x79f7ddbc2441757ebf08a6c42e2ffe3bc7a628bc"

def search_direct_private_key_storage():
    """直接搜索私钥存储"""
    print("=== 直接搜索私钥存储 ===")
    
    found_keys = []
    
    # 1. 搜索所有JSON文件
    print("1. 搜索JSON文件...")
    for root, dirs, files in os.walk("epik_data_final"):
        for file in files:
            if file.endswith('.json'):
                filepath = os.path.join(root, file)
                print(f"  检查: {os.path.relpath(filepath, 'epik_data_final')}")
                
                try:
                    with open(filepath, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # 解析JSON
                    try:
                        json_data = json.loads(content)
                        keys = search_json_for_wallet_data(json_data, filepath)
                        found_keys.extend(keys)
                    except:
                        # 如果不是有效JSON，搜索文本中的私钥模式
                        text_keys = search_text_for_private_keys(content, filepath)
                        found_keys.extend(text_keys)
                        
                except Exception as e:
                    print(f"    读取失败: {e}")
    
    # 2. 搜索所有XML文件中的明文私钥
    print("\n2. 搜索XML文件...")
    for root, dirs, files in os.walk("epik_data_final"):
        for file in files:
            if file.endswith('.xml'):
                filepath = os.path.join(root, file)
                print(f"  检查: {os.path.relpath(filepath, 'epik_data_final')}")
                
                try:
                    with open(filepath, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # 搜索私钥模式
                    text_keys = search_text_for_private_keys(content, filepath)
                    found_keys.extend(text_keys)
                    
                    # 解析XML结构
                    try:
                        tree = ET.parse(filepath)
                        root_elem = tree.getroot()
                        xml_keys = search_xml_for_wallet_data(root_elem, filepath)
                        found_keys.extend(xml_keys)
                    except:
                        pass
                        
                except Exception as e:
                    print(f"    读取失败: {e}")
    
    # 3. 搜索数据库中的明文存储
    print("\n3. 搜索数据库文件...")
    db_files = []
    for root, dirs, files in os.walk("epik_data_final"):
        for file in files:
            if file.endswith('.db'):
                db_files.append(os.path.join(root, file))
    
    for db_file in db_files:
        print(f"  检查数据库: {os.path.relpath(db_file, 'epik_data_final')}")
        db_keys = search_database_for_wallet_data(db_file)
        found_keys.extend(db_keys)
    
    # 4. 搜索其他可能的钱包文件
    print("\n4. 搜索钱包相关文件...")
    wallet_extensions = ['.wallet', '.key', '.keystore', '.dat', '.bin']
    for root, dirs, files in os.walk("epik_data_final"):
        for file in files:
            if any(file.endswith(ext) for ext in wallet_extensions):
                filepath = os.path.join(root, file)
                print(f"  检查钱包文件: {os.path.relpath(filepath, 'epik_data_final')}")
                
                try:
                    with open(filepath, 'rb') as f:
                        content = f.read()
                    
                    # 尝试作为文本解析
                    try:
                        text_content = content.decode('utf-8')
                        text_keys = search_text_for_private_keys(text_content, filepath)
                        found_keys.extend(text_keys)
                    except:
                        # 二进制文件，搜索二进制模式
                        binary_keys = search_binary_for_private_keys(content, filepath)
                        found_keys.extend(binary_keys)
                        
                except Exception as e:
                    print(f"    读取失败: {e}")
    
    return found_keys

def search_json_for_wallet_data(json_data, filepath):
    """在JSON数据中搜索钱包数据"""
    found_keys = []
    
    def search_recursive(obj, path=""):
        if isinstance(obj, dict):
            for key, value in obj.items():
                current_path = f"{path}.{key}" if path else key
                
                # 检查键名是否包含钱包相关词汇
                if any(keyword in key.lower() for keyword in ['private', 'key', 'secret', 'mnemonic', 'seed', 'wallet', 'account']):
                    print(f"    找到钱包相关字段: {current_path}")
                    print(f"      值: {str(value)[:100]}...")
                    
                    if isinstance(value, str):
                        # 检查是否是私钥格式
                        if len(value) == 64 and re.match(r'^[a-fA-F0-9]{64}$', value):
                            found_keys.append({
                                'type': 'private_key',
                                'value': value,
                                'source': f"{filepath}:{current_path}",
                                'format': 'hex'
                            })
                            print(f"      *** 可能的私钥: {value} ***")
                        
                        # 检查是否是地址
                        elif value.lower() == TARGET_ADDRESS.lower():
                            print(f"      *** 找到目标地址！***")
                            found_keys.append({
                                'type': 'target_address',
                                'value': value,
                                'source': f"{filepath}:{current_path}",
                                'format': 'address'
                            })
                        
                        # 检查是否是助记词
                        elif len(value.split()) >= 12:
                            words = value.split()
                            if all(len(word) >= 3 for word in words[:12]):
                                found_keys.append({
                                    'type': 'mnemonic',
                                    'value': value,
                                    'source': f"{filepath}:{current_path}",
                                    'format': 'words'
                                })
                                print(f"      *** 可能的助记词: {value[:50]}... ***")
                
                # 递归搜索
                search_recursive(value, current_path)
                
        elif isinstance(obj, list):
            for i, item in enumerate(obj):
                search_recursive(item, f"{path}[{i}]")
    
    search_recursive(json_data)
    return found_keys

def search_xml_for_wallet_data(root_elem, filepath):
    """在XML中搜索钱包数据"""
    found_keys = []
    
    def search_xml_recursive(elem, path=""):
        current_path = f"{path}/{elem.tag}" if path else elem.tag
        
        # 检查元素的文本内容
        if elem.text:
            text = elem.text.strip()
            
            # 检查是否是私钥
            if len(text) == 64 and re.match(r'^[a-fA-F0-9]{64}$', text):
                found_keys.append({
                    'type': 'private_key',
                    'value': text,
                    'source': f"{filepath}:{current_path}",
                    'format': 'hex'
                })
                print(f"    在XML中找到可能的私钥: {text}")
            
            # 检查是否是目标地址
            elif text.lower() == TARGET_ADDRESS.lower():
                found_keys.append({
                    'type': 'target_address',
                    'value': text,
                    'source': f"{filepath}:{current_path}",
                    'format': 'address'
                })
                print(f"    *** 在XML中找到目标地址！***")
        
        # 检查属性
        for attr_name, attr_value in elem.attrib.items():
            if len(attr_value) == 64 and re.match(r'^[a-fA-F0-9]{64}$', attr_value):
                found_keys.append({
                    'type': 'private_key',
                    'value': attr_value,
                    'source': f"{filepath}:{current_path}@{attr_name}",
                    'format': 'hex'
                })
                print(f"    在XML属性中找到可能的私钥: {attr_value}")
        
        # 递归搜索子元素
        for child in elem:
            search_xml_recursive(child, current_path)
    
    search_xml_recursive(root_elem)
    return found_keys

def search_text_for_private_keys(text, filepath):
    """在文本中搜索私钥模式"""
    found_keys = []
    
    # 搜索64位十六进制字符串
    hex_patterns = re.findall(r'[a-fA-F0-9]{64}', text)
    for pattern in hex_patterns:
        found_keys.append({
            'type': 'private_key',
            'value': pattern,
            'source': filepath,
            'format': 'hex'
        })
    
    # 搜索目标地址
    if TARGET_ADDRESS.lower() in text.lower():
        found_keys.append({
            'type': 'target_address',
            'value': TARGET_ADDRESS,
            'source': filepath,
            'format': 'address'
        })
        print(f"    *** 在文本中找到目标地址！***")
    
    return found_keys

def search_database_for_wallet_data(db_file):
    """在数据库中搜索钱包数据"""
    found_keys = []
    
    try:
        conn = sqlite3.connect(db_file)
        cursor = conn.cursor()
        
        # 获取所有表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        for table_name in tables:
            table = table_name[0]
            
            try:
                cursor.execute(f"SELECT * FROM {table};")
                rows = cursor.fetchall()
                
                for row_idx, row in enumerate(rows):
                    for col_idx, value in enumerate(row):
                        if isinstance(value, str):
                            # 检查是否是私钥
                            if len(value) == 64 and re.match(r'^[a-fA-F0-9]{64}$', value):
                                found_keys.append({
                                    'type': 'private_key',
                                    'value': value,
                                    'source': f"{db_file}:{table}[{row_idx}][{col_idx}]",
                                    'format': 'hex'
                                })
                                print(f"    在数据库中找到可能的私钥: {value}")
                            
                            # 检查是否是目标地址
                            elif value.lower() == TARGET_ADDRESS.lower():
                                found_keys.append({
                                    'type': 'target_address',
                                    'value': value,
                                    'source': f"{db_file}:{table}[{row_idx}][{col_idx}]",
                                    'format': 'address'
                                })
                                print(f"    *** 在数据库中找到目标地址！***")
                
            except Exception as e:
                continue
        
        conn.close()
        
    except Exception as e:
        print(f"    数据库访问失败: {e}")
    
    return found_keys

def search_binary_for_private_keys(content, filepath):
    """在二进制文件中搜索私钥"""
    found_keys = []
    
    # 搜索32字节的潜在私钥
    for i in range(len(content) - 31):
        candidate_bytes = content[i:i+32]
        candidate_hex = binascii.hexlify(candidate_bytes).decode()
        
        # 检查是否看起来像私钥
        if len(set(candidate_hex)) > 8:  # 有足够的随机性
            found_keys.append({
                'type': 'private_key',
                'value': candidate_hex,
                'source': f"{filepath}:offset_{i}",
                'format': 'hex'
            })
    
    return found_keys

def main():
    """主函数"""
    print("EpiK Portal 直接钱包文件搜索工具")
    print(f"目标地址: {TARGET_ADDRESS}")
    print("直接在解包的应用数据中搜索钱包文件和私钥存储")
    print("=" * 80)
    
    # 直接搜索私钥存储
    found_keys = search_direct_private_key_storage()
    
    # 分析结果
    print(f"\n" + "=" * 80)
    print("搜索结果汇总")
    print("=" * 80)
    
    if found_keys:
        print(f"找到 {len(found_keys)} 个相关项目:")
        
        # 按类型分组
        by_type = {}
        for item in found_keys:
            item_type = item['type']
            if item_type not in by_type:
                by_type[item_type] = []
            by_type[item_type].append(item)
        
        for item_type, items in by_type.items():
            print(f"\n{item_type.upper()} ({len(items)} 个):")
            for item in items:
                print(f"  值: {item['value'][:64]}{'...' if len(item['value']) > 64 else ''}")
                print(f"  来源: {item['source']}")
                print(f"  格式: {item['format']}")
                print()
        
        # 保存结果
        with open("direct_wallet_search_results.json", "w") as f:
            json.dump({
                'target_address': TARGET_ADDRESS,
                'found_items': found_keys,
                'summary': {item_type: len(items) for item_type, items in by_type.items()}
            }, f, indent=2)
        
        print(f"详细结果已保存到: direct_wallet_search_results.json")
        
    else:
        print("未找到明文存储的私钥或钱包文件")
        print("\n可能的原因:")
        print("1. 私钥被加密存储")
        print("2. 使用了Android Keystore")
        print("3. 私钥存储在其他格式的文件中")
        print("4. 需要进一步解密加密的配置数据")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
