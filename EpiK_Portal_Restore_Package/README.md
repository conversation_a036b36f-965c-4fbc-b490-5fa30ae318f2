# EpiK Portal 应用恢复指南

## 概述
此恢复包包含了从小米手机备份中提取的完整EpiK Portal应用数据，包括：
- 应用APK文件
- 钱包配置数据
- 数据库文件
- WebView缓存
- 其他应用文件

## 恢复方法

### 方法一：使用ADB恢复（推荐）

#### 前提条件：
1. 新手机已获得root权限
2. 开启USB调试模式
3. 电脑安装ADB工具

#### 步骤：
1. 将手机连接到电脑
2. 运行恢复脚本：
   ```bash
   ./restore_with_adb.sh
   ```
3. 等待脚本完成
4. 重启EpiK Portal应用

### 方法二：小米备份恢复

#### 步骤：
1. 运行备份创建脚本：
   ```bash
   ./create_miui_backup.sh
   ```

2. 将生成的备份文件传输到新手机：

   **方法A：通过USB传输**
   ```bash
   # 连接新手机到电脑
   adb push miui_backup /sdcard/MIUI/backup/AllBackup/
   ```

   **方法B：手动复制**
   - 将 `miui_backup` 目录复制到U盘或云盘
   - 在新手机上下载到：`/sdcard/MIUI/backup/AllBackup/`
   - 或者放到：`/storage/emulated/0/MIUI/backup/AllBackup/`

3. 在新手机上使用小米备份工具恢复：
   - 打开 **设置** → **更多设置** → **备份与重置** → **本地备份**
   - 选择 **恢复** → 找到刚才复制的备份文件
   - 选择 **EpiK Portal** 应用进行恢复
   - 等待恢复完成后重启手机

### 方法三：手动恢复

#### 前提条件：
- 手机已root
- 安装文件管理器（如RE管理器）

#### 步骤：
1. 手动安装`EpiK_Portal.apk`
2. 将`app_data`目录下的文件复制到：
   `/data/data/com.epik.wallet/`
3. 设置正确的文件权限
4. 重启应用

## 重要提示

### 安全警告：
- 此恢复包包含敏感的钱包数据
- 请妥善保管，避免泄露
- 建议在安全的环境中进行恢复操作

### 兼容性：
- 适用于Android 7.0及以上版本
- 建议在相同或更新版本的MIUI上恢复
- 如果目标设备架构不同，可能需要重新下载APK

### 故障排除：

#### 应用无法启动：
1. 检查文件权限是否正确
2. 确认应用版本兼容性
3. 清除应用缓存后重试

#### 数据未恢复：
1. 确认数据文件已正确复制
2. 检查目录结构是否正确
3. 重启设备后再试

#### 钱包无法访问：
1. 确认密码是否正确
2. 检查网络连接
3. 可能需要重新导入助记词

## 文件说明

- `EpiK_Portal.apk`: 应用安装包
- `app_data/shared_prefs/`: 应用配置文件
- `app_data/databases/`: 数据库文件
- `app_data/app_webview/`: WebView数据
- `app_data/files/`: 其他应用文件

## 联系支持

如果恢复过程中遇到问题，请：
1. 检查本文档的故障排除部分
2. 确认操作步骤是否正确
3. 考虑寻求专业技术支持

---
恢复包创建时间: $(date)
原始备份来源: EpiK Portal(com.epik.wallet).bak
