#!/usr/bin/env python3
"""
EpiK Portal 快速恢复工具
适用于新小米手机的一键恢复
"""

import os
import subprocess
import sys
import time

def check_adb_connection():
    """检查ADB连接"""
    print("检查ADB连接...")
    try:
        result = subprocess.run(['adb', 'devices'], capture_output=True, text=True)
        if 'device' in result.stdout and not result.stdout.count('device') == 1:
            print("✓ 检测到ADB设备连接")
            return True
        else:
            print("✗ 未检测到ADB设备")
            print("请确保:")
            print("1. 手机已开启USB调试")
            print("2. 已授权此电脑的ADB调试")
            print("3. ADB工具已正确安装")
            return False
    except FileNotFoundError:
        print("✗ 未找到ADB工具")
        print("请安装Android SDK Platform Tools")
        return False

def check_root_access():
    """检查root权限"""
    print("检查root权限...")
    try:
        result = subprocess.run(['adb', 'shell', 'su', '-c', 'id'], capture_output=True, text=True)
        if 'uid=0' in result.stdout:
            print("✓ 检测到root权限")
            return True
        else:
            print("✗ 未检测到root权限")
            print("此恢复需要root权限来访问应用数据目录")
            return False
    except:
        print("✗ root权限检查失败")
        return False

def install_apk():
    """安装APK"""
    print("\n安装EpiK Portal应用...")
    try:
        result = subprocess.run(['adb', 'install', '-r', 'EpiK_Portal.apk'], 
                              capture_output=True, text=True)
        if 'Success' in result.stdout:
            print("✓ APK安装成功")
            return True
        else:
            print(f"✗ APK安装失败: {result.stdout}")
            return False
    except Exception as e:
        print(f"✗ APK安装出错: {e}")
        return False

def restore_app_data():
    """恢复应用数据"""
    print("\n恢复应用数据...")
    
    # 停止应用（如果正在运行）
    print("停止EpiK Portal应用...")
    subprocess.run(['adb', 'shell', 'am', 'force-stop', 'com.epik.wallet'], 
                   capture_output=True)
    
    # 创建应用数据目录
    print("创建应用数据目录...")
    subprocess.run(['adb', 'shell', 'su', '-c', 
                   'mkdir -p /data/data/com.epik.wallet'], capture_output=True)
    
    # 推送数据文件
    data_dirs = [
        ('app_data/shared_prefs', '/data/data/com.epik.wallet/shared_prefs'),
        ('app_data/databases', '/data/data/com.epik.wallet/databases'),
        ('app_data/files', '/data/data/com.epik.wallet/files'),
        ('app_data/app_webview', '/data/data/com.epik.wallet/app_webview')
    ]
    
    for local_dir, remote_dir in data_dirs:
        if os.path.exists(local_dir):
            print(f"推送 {local_dir}...")
            result = subprocess.run(['adb', 'push', local_dir, remote_dir], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✓ {local_dir} 推送成功")
            else:
                print(f"✗ {local_dir} 推送失败: {result.stderr}")
    
    # 设置文件权限
    print("设置文件权限...")
    uid_result = subprocess.run(['adb', 'shell', 'dumpsys', 'package', 'com.epik.wallet'], 
                               capture_output=True, text=True)
    
    # 简化权限设置
    subprocess.run(['adb', 'shell', 'su', '-c', 
                   'chown -R system:system /data/data/com.epik.wallet'], 
                   capture_output=True)
    subprocess.run(['adb', 'shell', 'su', '-c', 
                   'chmod -R 755 /data/data/com.epik.wallet'], 
                   capture_output=True)
    
    print("✓ 数据恢复完成")

def verify_restore():
    """验证恢复结果"""
    print("\n验证恢复结果...")
    
    # 检查关键文件是否存在
    key_files = [
        '/data/data/com.epik.wallet/shared_prefs/FlutterSharedPreferences.xml',
        '/data/data/com.epik.wallet/databases/ua.db'
    ]
    
    all_good = True
    for file_path in key_files:
        result = subprocess.run(['adb', 'shell', 'su', '-c', f'ls {file_path}'], 
                               capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✓ {file_path} 存在")
        else:
            print(f"✗ {file_path} 不存在")
            all_good = False
    
    return all_good

def main():
    """主函数"""
    print("=" * 60)
    print("EpiK Portal 快速恢复工具")
    print("=" * 60)
    
    print("\n重要提示:")
    print("1. 请确保新手机已获得root权限")
    print("2. 已开启USB调试模式")
    print("3. 此过程将覆盖现有的EpiK Portal数据")
    
    response = input("\n是否继续? (y/N): ")
    if response.lower() != 'y':
        print("操作已取消")
        return
    
    # 检查前提条件
    if not check_adb_connection():
        return
    
    if not check_root_access():
        return
    
    # 执行恢复步骤
    print("\n" + "=" * 60)
    print("开始恢复过程...")
    print("=" * 60)
    
    # 1. 安装APK
    if not install_apk():
        print("APK安装失败，无法继续")
        return
    
    # 等待安装完成
    print("等待安装完成...")
    time.sleep(3)
    
    # 2. 恢复数据
    restore_app_data()
    
    # 3. 验证恢复
    if verify_restore():
        print("\n" + "=" * 60)
        print("✓ 恢复完成！")
        print("=" * 60)
        print("\n后续步骤:")
        print("1. 启动EpiK Portal应用")
        print("2. 使用密码 'zyuu23521' 解锁钱包")
        print("3. 验证钱包地址和余额")
        print("4. 建议立即备份助记词")
        
        print(f"\n目标钱包地址: 0x79f7ddbc2441757ebf08a6c42e2ffe3bc7a628bc")
        print("如果地址不匹配，请检查恢复是否成功")
        
    else:
        print("\n" + "=" * 60)
        print("✗ 恢复验证失败")
        print("=" * 60)
        print("请检查:")
        print("1. root权限是否正常")
        print("2. 文件是否正确推送")
        print("3. 尝试手动恢复方法")

if __name__ == "__main__":
    main()
