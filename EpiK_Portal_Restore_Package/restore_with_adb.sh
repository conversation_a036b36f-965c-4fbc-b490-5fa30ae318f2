#!/bin/bash
# EpiK Portal ADB恢复脚本
# 需要root权限和ADB调试

echo "=== EpiK Portal 数据恢复脚本 ==="
echo "请确保："
echo "1. 手机已开启USB调试"
echo "2. 已获得root权限"
echo "3. 已安装ADB工具"
echo ""

# 检查ADB连接
if ! adb devices | grep -q "device$"; then
    echo "错误: 未检测到ADB设备连接"
    exit 1
fi

echo "检测到ADB设备连接"

# 安装APK
echo "安装EpiK Portal应用..."
adb install -r EpiK_Portal.apk

if [ $? -eq 0 ]; then
    echo "✓ APK安装成功"
else
    echo "✗ APK安装失败"
    exit 1
fi

# 等待安装完成
sleep 3

# 恢复应用数据
echo "恢复应用数据..."

# 推送数据文件到手机
adb push app_data/shared_prefs /data/data/com.epik.wallet/shared_prefs/
adb push app_data/databases /data/data/com.epik.wallet/databases/
adb push app_data/files /data/data/com.epik.wallet/files/
adb push app_data/app_webview /data/data/com.epik.wallet/app_webview/

# 设置正确的权限
adb shell "su -c 'chown -R u0_a$(adb shell dumpsys package com.epik.wallet | grep userId | cut -d= -f2):u0_a$(adb shell dumpsys package com.epik.wallet | grep userId | cut -d= -f2) /data/data/com.epik.wallet/'"
adb shell "su -c 'chmod -R 755 /data/data/com.epik.wallet/'"

echo "✓ 数据恢复完成"
echo "请重启EpiK Portal应用以加载恢复的数据"
