#!/bin/bash
# 小米备份格式恢复脚本

echo "=== 创建小米备份格式 ==="

# 检查必要文件是否存在
if [ ! -d "app_data" ]; then
    echo "错误: 未找到 app_data 目录"
    echo "请确保在 EpiK_Portal_Restore_Package 目录中运行此脚本"
    exit 1
fi

if [ ! -f "EpiK_Portal.apk" ]; then
    echo "错误: 未找到 EpiK_Portal.apk 文件"
    exit 1
fi

# 创建小米备份目录结构
echo "创建备份目录结构..."
mkdir -p miui_backup/apps/com.epik.wallet

# 复制数据到小米备份格式
echo "复制应用数据..."
if [ -d "app_data" ]; then
    cp -r app_data/* miui_backup/apps/com.epik.wallet/
    echo "✓ 应用数据复制完成"
else
    echo "✗ app_data 目录不存在"
    exit 1
fi

echo "复制APK文件..."
cp EpiK_Portal.apk miui_backup/apps/com.epik.wallet/base.apk
echo "✓ APK文件复制完成"

# 创建备份清单
echo "创建备份清单..."
cat > miui_backup/apps/com.epik.wallet/_manifest << EOF
1
com.epik.wallet
118
30

1
1
EOF

echo "✓ 备份清单创建完成"

# 显示备份信息
echo ""
echo "=== 备份创建完成 ==="
echo "备份位置: $(pwd)/miui_backup/"
echo "备份大小: $(du -sh miui_backup | cut -f1)"
echo ""
echo "下一步操作："
echo "1. 将 miui_backup 目录传输到新手机"
echo "2. 放置到: /sdcard/MIUI/backup/AllBackup/"
echo "3. 使用小米备份工具恢复"
echo ""
echo "详细步骤请参考: 小米备份恢复详细指南.md"
