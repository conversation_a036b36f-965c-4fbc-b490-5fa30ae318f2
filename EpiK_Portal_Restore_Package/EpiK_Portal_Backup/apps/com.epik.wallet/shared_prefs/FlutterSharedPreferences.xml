<?xml version='1.0' encoding='utf-8' standalone='yes' ?>
<map>
    <string name="flutter.eev-cid-*********"></string>
    <string name="flutter.MinerIds_*********">{&quot;list&quot;:[&quot;f042571&quot;],&quot;current&quot;:&quot;f042571&quot;}</string>
    <string name="flutter.am_account_list">1uuLeAI+tWqO2jeBBPDMC6C9IXH/r8EhBTbyZg9p/TtdzbWzT34jDgxjvGLGJufFDe7nKBrWQFvwK52nzhwBX4k6rtEiI3J9cZJKZG+GSjGrem7upZetiGpuhX9wGDLrghB1M9JTgc/6K8rVOPVNKAtZ61meTY5H2XWTMm4KUvhxUAtxNur01YrUS3UWGODBsw0SekvT0gCGIKWCeVP1Ht+PE8TcQlr/UliPrx0qgSC4fTY3/q7b+GS0PwUmxSuaZbUD6Fj3mMT6XxdCTGAVM2pVqxin6NAjp9XI4w7b0V/MMroWp0nopWm8ZWs+E62+dLx2QWqJtqVq6sETH78ZqdhCnjsVzZxUtDoekZpvSvCwdSy9ngGkQSMfGcue2VRIv7Rmel/bXflKzLRGydwMDMXtbKl/6fdT5sCbmwqBhFm+JzPCuAj8ZPQO98S4MtSyzsDa6lWLCwHLimZ93wqZsMSOaKU9kNL5Pxe6JdJSqCQE3EH/WL4LulpYpROov5oHQe/3qQZ6g0lMNWV2Ag/CFNs8BEmxcbR1EwOMyiXpNpb1sBEGuqqWGLJMB7HOYbuyKsf6aUrKwaF5I1nIwKmOFTHtbrLbXbR0Td385UHSj0NXycgBjmsmkif46yTLYd9C8Nnt2cHk9UPjs/p6C2O9NbxrnJCpYyH+BP2L/qobQd8zvwG09oJ/118BJ1Q59YxJbnil8CYMsLACKNosh3K/5ex7Ywhkt93lxxmGLpSXKJQbLf2mS/CZnYqcyU2JBJOds4ZC3UJrLtejlL9nPs9pCRq5NejV0z8g4YaybKca+5uQ8lhvD6OZ1TLTY+lGuJIs6XaT5CEQLj4eLKV2bFXgNSqdIaOdr8jRNDI8ltget888MqFmsO/Tth7S7zuNT9LCAUhfXKtpNBsWZO9LGHXf417Zp4sY560T+dYRlhUTIdr88P3OOzvXxFzU2J2q3OgDDY82hhOW8uOlBIUOj50DAGSlVHpXvFVpfgsO7LY4TFqy5qcIWuqyjVWqWikh8dKGU9TfbwOj3FGUFrIInpirx6ucgqhlnwYmQkM1/D0SJ69gjrx5541e7AJ7KQwnnqW0rITa5rLs+1rxuQ6SRvFyprc4Kzm5Raaydn4bjDTED3t7iZvi7k0ErdRoqaRjzHxJD7w8Qrb1I3tIHK1rqXfQmfBDK3kaflenaDXXwQ9s7+jaR8Nz9DaU3m0mgDzWYsMp</string>
    <string name="flutter.eev-txhash-*********">0xb81331959804b20cfba56438ad8a6f1db7215a211b755cc02927c7db5db4443a</string>
    <string name="flutter.MinerIds_1025940402">{&quot;list&quot;:[&quot;f044398&quot;,&quot;f044557&quot;,&quot;f044653&quot;,&quot;f044677&quot;,&quot;f044687&quot;,&quot;f044692&quot;,&quot;f044695&quot;,&quot;f044824&quot;],&quot;current&quot;:&quot;f044824&quot;}</string>
    <string name="flutter.serverconfig">{&quot;code&quot;:{&quot;code&quot;:0,&quot;message&quot;:&quot;OK&quot;},&quot;config&quot;:{&quot;Android&quot;:{&quot;LatestVersion&quot;:&quot;1.1.8&quot;,&quot;RequiredVersion&quot;:&quot;1.0.0&quot;,&quot;UpdateURL&quot;:&quot;https://cdn.epik-protocol.io/epik-portal_1_1_8.apk&quot;,&quot;description&quot;:&quot;增加显示投票锁定中与已解锁余额;&quot;},&quot;IOS&quot;:{&quot;LatestVersion&quot;:&quot;1.1.8&quot;,&quot;RequiredVersion&quot;:&quot;1.0.0&quot;,&quot;UpdateURL&quot;:&quot;https://tf.blockchaintestflight.com/app/1582114618&quot;,&quot;description&quot;:&quot;增加显示投票锁定中与已解锁余额;\nIOS版本升级后会在手机上创建一个新的图标，请备份好旧版本的助记词，在新版本钱包重新导入;&quot;},&quot;WalletAPI&quot;:&quot;https://explorer.epik-protocol.io/api/&quot;,&quot;ETHAPI&quot;:&quot;wss://mainnet.infura.io/ws/v3/********************************&quot;,&quot;EPKAPI&quot;:&quot;ws://*************:1234/rpc/v0&quot;,&quot;EPKToken&quot;:&quot;eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJBbGxvdyI6WyJyZWFkIiwid3JpdGUiLCJzaWduIiwiYWRtaW4iXX0.Wh1YexzNgNskHOKR8LKG5Y8XzDgXi2saPWp755OSHFM&quot;,&quot;EPKNetwork&quot;:&quot;Mainnet&quot;,&quot;MinSwap&quot;:200,&quot;SignWeixin&quot;:&quot;Sigrid_EpiK&quot;,&quot;SignTele&quot;:&quot;https://t.me/EpikProtocol&quot;},&quot;home_list_ch&quot;:[{&quot;Name&quot;:&quot;跨链换币&quot;,&quot;Invalid&quot;:false,&quot;Icon&quot;:&quot;&quot;,&quot;Action&quot;:&quot;swap&quot;},{&quot;Name&quot;:&quot;应用&quot;,&quot;Invalid&quot;:false,&quot;Icon&quot;:&quot;&quot;,&quot;Action&quot;:&quot;dapp&quot;},{&quot;Name&quot;:&quot;Uniswap&quot;,&quot;Invalid&quot;:false,&quot;Icon&quot;:&quot;&quot;,&quot;Action&quot;:&quot;uniswap&quot;},{&quot;Name&quot;:&quot;扫一扫&quot;,&quot;Invalid&quot;:false,&quot;Icon&quot;:&quot;&quot;,&quot;Action&quot;:&quot;scan&quot;},{&quot;Name&quot;:&quot;钱包设置&quot;,&quot;Invalid&quot;:false,&quot;Icon&quot;:&quot;&quot;,&quot;Action&quot;:&quot;setting&quot;}],&quot;home_list_en&quot;:[{&quot;Name&quot;:&quot;EPK Swap&quot;,&quot;Invalid&quot;:false,&quot;Icon&quot;:&quot;&quot;,&quot;Action&quot;:&quot;swap&quot;},{&quot;Name&quot;:&quot;Application&quot;,&quot;Invalid&quot;:false,&quot;Icon&quot;:&quot;&quot;,&quot;Action&quot;:&quot;dapp&quot;},{&quot;Name&quot;:&quot;Uniswap&quot;,&quot;Invalid&quot;:false,&quot;Icon&quot;:&quot;&quot;,&quot;Action&quot;:&quot;uniswap&quot;},{&quot;Name&quot;:&quot;Scan&quot;,&quot;Invalid&quot;:false,&quot;Icon&quot;:&quot;&quot;,&quot;Action&quot;:&quot;scan&quot;},{&quot;Name&quot;:&quot;Setting&quot;,&quot;Invalid&quot;:false,&quot;Icon&quot;:&quot;&quot;,&quot;Action&quot;:&quot;setting&quot;}]}</string>
</map>
