{"target_address": "0x79f7ddbc2441757ebf08a6c42e2ffe3bc7a628bc", "summary": {"flutter_matches": 0, "database_matches": 1, "file_matches": 0, "total_related_files": 7}, "detailed_findings": {"flutter_preferences": {"flutter.MinerIds_293794365": {"type": "account_related", "value": "{\"list\":[\"f042571\"],\"current\":\"f042571\"}", "length": 40, "parsed_json": {"list": ["f042571"], "current": "f042571"}}, "flutter.am_account_list": {"type": "account_related", "value": "1uuLeAI+tWqO2jeBBPDMC6C9IXH/r8EhBTbyZg9p/TtdzbWzT34jDgxjvGLGJufFDe7nKBrWQFvwK52nzhwBX4k6rtEiI3J9cZJKZG+GSjGrem7upZetiGpuhX9wGDLrghB1M9JTgc/6K8rVOPVNKAtZ61meTY5H2XWTMm4KUvhxUAtxNur01YrUS3UWGODBsw0SekvT0gCGIKWCeVP1Ht+PE8TcQlr/UliPrx0qgSC4fTY3/q7b+GS0PwUmxSuaZbUD6Fj3mMT6XxdCTGAVM2pVqxin6NAjp9XI4w7b0V/MMroWp0nopWm8ZWs+E62+dLx2QWqJtqVq6sETH78ZqdhCnjsVzZxUtDoekZpvSvCwdSy9ngGkQSMfGcue2VRIv7Rmel/bXflKzLRGydwMDMXtbKl/6fdT5sCbmwqBhFm+JzPCuAj8ZPQO98S4MtSyzsDa6lWLCwHLimZ93wqZsMSOaKU9kNL5Pxe6JdJSqCQE3EH/WL4LulpYpROov5oHQe/3qQZ6g0lMNWV2Ag/CFNs8BEmxcbR1EwOMyiXpNpb1sBEGuqqWGLJMB7HOYbuyKsf6aUrKwaF5I1nIwKmOFTHtbrLbXbR0Td385UHSj0NXycgBjmsmkif46yTLYd9C8Nnt2cHk9UPjs/p6C2O9NbxrnJCpYyH+BP2L/qobQd8zvwG09oJ/118BJ1Q59YxJbnil8CYMsLACKNosh3K/5ex7Ywhkt93lxxmGLpSXKJQbLf2mS/CZnYqcyU2JBJOds4ZC3UJrLtejlL9nPs9pCRq5NejV0z8g4YaybKca+5uQ8lhvD6OZ1TLTY+lGuJIs6XaT5CEQLj4eLKV2bFXgNSqdIaOdr8jRNDI8ltget888MqFmsO/Tth7S7zuNT9LCAUhfXKtpNBsWZO9LGHXf417Zp4sY560T+dYRlhUTIdr88P3OOzvXxFzU2J2q3OgDDY82hhOW8uOlBIUOj50DAGSlVHpXvFVpfgsO7LY4TFqy5qcIWuqyjVWqWikh8dKGU9TfbwOj3FGUFrIInpirx6ucgqhlnwYmQkM1/D0SJ69gjrx5541e7AJ7KQwnnqW0rITa5rLs+1rxuQ6SRvFyprc4Kzm5Raaydn4bjDTED3t7iZvi7k0ErdRoqaRjzHxJD7w8Qrb1I3tIHK1rqXfQmfBDK3kaflenaDXXwQ9s7+jaR8Nz9DaU3m0mgDzWYsMp", "length": 1216, "base64_decoded": {"length": 912, "hex_preview": "d6eb8b78023eb56a8eda378104f0cc0ba0bd2171ffafc1210536f2660f69fd3b5dcdb5b34f7e230e0c63bc62c626e7c50dee", "text_preview": "x\u0002>j7\u0004\u000b!q!\u00056f\u000fi;]͵O~#\u000e\fcb&\r(\u001a@[+\u001c\u0001_:\"#r}qJdoJ1znjnp\u00182\u0010u3"}}, "flutter.MinerIds_1025940402": {"type": "account_related", "value": "{\"list\":[\"f044398\",\"f044557\",\"f044653\",\"f044677\",\"f044687\",\"f044692\",\"f044695\",\"f044824\"],\"current\":\"f044824\"}", "length": 110, "parsed_json": {"list": ["f044398", "f044557", "f044653", "f044677", "f044687", "f044692", "f044695", "f044824"], "current": "f044824"}}}, "databases": {"ua.db": {"tables": {"android_metadata": {"columns": ["locale"], "rows": 1, "matches": []}, "__sd": {"columns": ["id", "__ii", "__a", "__b", "__c", "__d", "__e", "__f", "__g", "__sp", "__pp", "__av", "__vc"], "rows": 1, "matches": []}, "sqlite_sequence": {"columns": ["name", "seq"], "rows": 2, "matches": []}, "__is": {"columns": ["id", "__ii", "__e", "__sp", "__pp", "__av", "__vc"], "rows": 0, "matches": []}, "__et": {"columns": ["id", "__i", "__e", "__s", "__t", "__av", "__vc"], "rows": 0, "matches": []}, "__er": {"columns": ["id", "__i", "__a", "__t", "__av", "__vc"], "rows": 0, "matches": []}}, "address_matches": [], "related_data": []}, "libCachedImageData.db": {"tables": {"android_metadata": {"columns": ["locale"], "rows": 1, "matches": []}, "cacheObject": {"columns": ["_id", "url", "relativePath", "eTag", "validTill", "touched"], "rows": 4, "matches": [{"table": "cacheObject", "row_index": 1, "row_data": [2, "https://wenhairu.com/static/api/qr/?size=300&text=******************************************", "adf175c0-fe7a-11eb-adbf-81fdc4efd3a5.png", null, 1629713642268, 1629169488944], "match_type": "direct_address"}, {"table": "cacheObject", "row_index": 1, "row_data": [2, "https://wenhairu.com/static/api/qr/?size=300&text=******************************************", "adf175c0-fe7a-11eb-adbf-81fdc4efd3a5.png", null, 1629713642268, 1629169488944], "match_type": "address_fragment"}]}}, "address_matches": [{"table": "cacheObject", "row_index": 1, "row_data": [2, "https://wenhairu.com/static/api/qr/?size=300&text=******************************************", "adf175c0-fe7a-11eb-adbf-81fdc4efd3a5.png", null, 1629713642268, 1629169488944], "match_type": "direct_address"}], "related_data": [{"table": "cacheObject", "row_index": 1, "addresses": ["******************************************"], "tx_hashes": [], "row_data": [2, "https://wenhairu.com/static/api/qr/?size=300&text=******************************************", "adf175c0-fe7a-11eb-adbf-81fdc4efd3a5.png", null, 1629713642268, 1629169488944]}]}, "Web Data": {"tables": {"meta": {"columns": ["key", "value"], "rows": 3, "matches": []}, "autofill": {"columns": ["name", "value", "value_lower", "date_created", "date_last_used", "count"], "rows": 0, "matches": []}, "credit_cards": {"columns": ["guid", "name_on_card", "expiration_month", "expiration_year", "card_number_encrypted", "date_modified", "origin", "use_count", "use_date", "billing_address_id"], "rows": 0, "matches": []}, "autofill_profiles": {"columns": ["guid", "company_name", "street_address", "dependent_locality", "city", "state", "zipcode", "sorting_code", "country_code", "date_modified", "origin", "language_code", "use_count", "use_date", "validity_bitfield", "is_client_validity_states_updated"], "rows": 0, "matches": []}, "autofill_profile_names": {"columns": ["guid", "first_name", "middle_name", "last_name", "full_name"], "rows": 0, "matches": []}, "autofill_profile_emails": {"columns": ["guid", "email"], "rows": 0, "matches": []}, "autofill_profile_phones": {"columns": ["guid", "number"], "rows": 0, "matches": []}, "autofill_profiles_trash": {"columns": ["guid"], "rows": 0, "matches": []}, "masked_credit_cards": {"columns": ["id", "status", "name_on_card", "network", "last_four", "exp_month", "exp_year", "bank_name", "nickname"], "rows": 0, "matches": []}, "unmasked_credit_cards": {"columns": ["id", "card_number_encrypted", "use_count", "use_date", "unmask_date"], "rows": 0, "matches": []}, "server_card_metadata": {"columns": ["id", "use_count", "use_date", "billing_address_id"], "rows": 0, "matches": []}, "server_addresses": {"columns": ["id", "company_name", "street_address", "address_1", "address_2", "address_3", "address_4", "postal_code", "sorting_code", "country_code", "language_code", "recipient_name", "phone_number"], "rows": 0, "matches": []}, "server_address_metadata": {"columns": ["id", "use_count", "use_date", "has_converted"], "rows": 0, "matches": []}, "autofill_sync_metadata": {"columns": ["model_type", "storage_key", "value"], "rows": 0, "matches": []}, "autofill_model_type_state": {"columns": ["model_type", "value"], "rows": 0, "matches": []}, "payments_customer_data": {"columns": ["customer_id"], "rows": 0, "matches": []}, "payments_upi_vpa": {"columns": ["vpa"], "rows": 0, "matches": []}, "server_card_cloud_token_data": {"columns": ["id", "suffix", "exp_month", "exp_year", "card_art_url", "instrument_token"], "rows": 0, "matches": []}}, "address_matches": [], "related_data": []}, "Cookies": {"tables": {"meta": {"columns": ["key", "value"], "rows": 3, "matches": []}, "cookies": {"columns": ["creation_utc", "host_key", "name", "value", "path", "expires_utc", "is_secure", "is_httponly", "last_access_utc", "has_expires", "is_persistent", "priority", "encrypted_value", "samesite", "source_scheme"], "rows": 0, "matches": []}}, "address_matches": [], "related_data": []}}, "files": {"apps/com.epik.wallet/_manifest": {"size": 1669, "matches": [], "related_data": {"miner_ids": ["f003082010", "f088", "f06807", "f04"]}}, "apps/com.epik.wallet/sp/FlutterSharedPreferences.xml": {"size": 4722, "matches": [], "related_data": {"eth_addresses": ["******************************************"], "tx_hashes": ["******************************************1b755cc02927c7db5db4443a"], "miner_ids": ["f042571", "f042571", "f044398", "f044557", "f044653", "f044677", "f044687", "f044692", "f044695", "f044824", "f044824"]}}, "apps/com.epik.wallet/r/app_webview/variations_seed": {"size": 157, "matches": [], "related_data": {"miner_ids": ["f00567"]}}, "apps/com.epik.wallet/a/base.apk": {"size": 22916566, "matches": [], "related_data": {"miner_ids": ["f057", "f07", "f01", "f06", "f05", "f04", "f03", "f07", "f01", "f09", "f006", "f040", "f000", "f03", "f03", "f01", "f07", "f00", "f04", "f09", "f07", "f08", "f08", "f06", "f01", "f07", "f05", "f069", "f07", "f09", "f03", "f058", "f07", "f07", "f07", "f024", "f090", "f04", "f03", "f08", "f03"]}}, "apps/com.epik.wallet/f/.imprint": {"size": 1189, "matches": [], "related_data": {"miner_ids": ["f09"]}}, "apps/com.epik.wallet/db/libCachedImageData.db": {"size": 16384, "matches": [], "related_data": {"eth_addresses": ["******************************************"], "miner_ids": ["f08", "f08"]}}, "apps/com.epik.wallet/db/libCachedImageData.db-journal": {"size": 8720, "matches": [], "related_data": {"eth_addresses": ["******************************************"], "miner_ids": ["f08", "f08"]}}}}}