#!/usr/bin/env python3
"""
EpiK Portal 私钥提取工具
专门从 EpiK_Portal_Restore_Package 中提取目标地址的私钥
目标地址: 0x79F7DDbc2441757EBf08a6C42E2FFE3bc7A628bc
"""

import base64
import binascii
import json
import xml.etree.ElementTree as ET
import sqlite3
import os
import hashlib
import hmac
from pathlib import Path
import struct
import re

# 目标地址（统一格式）
TARGET_ADDRESS = "0x79f7ddbc2441757ebf08a6c42e2ffe3bc7a628bc"
KNOWN_PASSWORDS = ["zyuu23521", "123456", "password"]

def extract_encrypted_account_data():
    """从FlutterSharedPreferences.xml提取加密的账户数据"""
    print("=== 提取加密账户数据 ===")
    
    prefs_file = "EpiK_Portal_Restore_Package/app_data/shared_prefs/FlutterSharedPreferences.xml"
    
    if not Path(prefs_file).exists():
        print(f"错误: 文件不存在 {prefs_file}")
        return None
    
    try:
        tree = ET.parse(prefs_file)
        root = tree.getroot()
        
        for string_elem in root.findall('string'):
            name = string_elem.get('name')
            if name == 'flutter.am_account_list':
                encrypted_b64 = string_elem.text
                encrypted_data = base64.b64decode(encrypted_b64)
                
                print(f"✓ 找到加密账户数据")
                print(f"  Base64长度: {len(encrypted_b64)} 字符")
                print(f"  二进制长度: {len(encrypted_data)} 字节")
                print(f"  十六进制预览: {binascii.hexlify(encrypted_data[:32]).decode()}...")
                
                return encrypted_data
        
        print("✗ 未找到 flutter.am_account_list 数据")
        return None
        
    except Exception as e:
        print(f"✗ 解析XML文件失败: {e}")
        return None

def try_decrypt_with_password(encrypted_data, password):
    """尝试使用密码解密数据"""
    print(f"\n--- 尝试密码: {password} ---")

    # 尝试多种解密方法
    methods = [
        ("简单XOR解密", decrypt_simple_xor),
        ("基础AES解密", decrypt_basic_aes),
        ("密码哈希解密", decrypt_password_hash),
    ]

    for method_name, decrypt_func in methods:
        try:
            result = decrypt_func(encrypted_data, password)
            if result:
                print(f"✓ {method_name} 解密成功!")
                return result
        except Exception as e:
            print(f"✗ {method_name} 失败: {e}")

    return None

def decrypt_simple_xor(encrypted_data, password):
    """简单XOR解密"""
    password_bytes = password.encode('utf-8')
    key = hashlib.sha256(password_bytes).digest()

    decrypted = bytearray()
    for i, byte in enumerate(encrypted_data):
        decrypted.append(byte ^ key[i % len(key)])

    try:
        decrypted_str = decrypted.decode('utf-8', errors='ignore')
        if TARGET_ADDRESS.lower() in decrypted_str.lower() or 'private' in decrypted_str.lower() or '{' in decrypted_str:
            return decrypted_str
    except:
        pass

    return None

def decrypt_basic_aes(encrypted_data, password):
    """基础AES解密（无外部依赖）"""
    # 这里我们先跳过AES解密，因为需要外部库
    # 实际项目中可以使用pycryptodome
    return None

def decrypt_password_hash(encrypted_data, password):
    """使用密码哈希尝试解密"""
    # 尝试不同的哈希方法
    hash_methods = [
        hashlib.md5(password.encode()).digest(),
        hashlib.sha1(password.encode()).digest(),
        hashlib.sha256(password.encode()).digest(),
    ]

    for key in hash_methods:
        try:
            decrypted = bytearray()
            for i, byte in enumerate(encrypted_data):
                decrypted.append(byte ^ key[i % len(key)])

            decrypted_str = decrypted.decode('utf-8', errors='ignore')
            if TARGET_ADDRESS.lower() in decrypted_str.lower() or 'private' in decrypted_str.lower():
                return decrypted_str
        except:
            continue

    return None

def search_private_keys_in_data(encrypted_data):
    """在加密数据中搜索可能的私钥"""
    print("\n=== 搜索私钥模式 ===")
    
    hex_data = binascii.hexlify(encrypted_data).decode().lower()
    
    # 搜索32字节的私钥候选
    private_key_candidates = []
    
    for i in range(0, len(encrypted_data) - 31):
        candidate = encrypted_data[i:i+32]
        hex_candidate = binascii.hexlify(candidate).decode()
        
        # 检查是否可能是私钥（不全为0，不全为FF）
        if candidate != b'\x00' * 32 and candidate != b'\xff' * 32:
            # 简单验证：检查是否在有效范围内
            if candidate[0] != 0 or any(b != 0 for b in candidate[1:]):
                private_key_candidates.append((i, hex_candidate))
    
    print(f"找到 {len(private_key_candidates)} 个可能的32字节私钥候选")
    
    # 尝试验证每个候选私钥
    for offset, hex_key in private_key_candidates[:20]:  # 只检查前20个
        if verify_private_key_for_address(hex_key, TARGET_ADDRESS):
            print(f"✓ 找到匹配的私钥!")
            print(f"  偏移量: {offset}")
            print(f"  私钥: {hex_key}")
            return hex_key
    
    return None

def verify_private_key_for_address(private_key_hex, target_address):
    """验证私钥是否对应目标地址（简化版本）"""
    # 由于没有eth_keys库，我们先简单检查格式
    try:
        # 检查是否是64字符的十六进制字符串
        if len(private_key_hex) == 64 and all(c in '0123456789abcdefABCDEF' for c in private_key_hex):
            # 简单的启发式检查：私钥不应该全为0或全为F
            if private_key_hex != '0' * 64 and private_key_hex.lower() != 'f' * 64:
                return True
        return False
    except:
        return False

def search_in_databases():
    """搜索数据库文件中的私钥信息"""
    print("\n=== 搜索数据库文件 ===")
    
    db_files = [
        "EpiK_Portal_Restore_Package/app_data/databases/ua.db",
        "EpiK_Portal_Restore_Package/app_data/databases/libCachedImageData.db"
    ]
    
    for db_file in db_files:
        if Path(db_file).exists():
            print(f"\n检查数据库: {db_file}")
            try:
                conn = sqlite3.connect(db_file)
                cursor = conn.cursor()
                
                # 获取所有表
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
                tables = cursor.fetchall()
                
                for table_name in tables:
                    table = table_name[0]
                    print(f"  表: {table}")
                    
                    try:
                        cursor.execute(f"SELECT * FROM {table} LIMIT 5;")
                        rows = cursor.fetchall()
                        
                        for row in rows:
                            row_str = str(row).lower()
                            if TARGET_ADDRESS.lower() in row_str or 'private' in row_str:
                                print(f"    ✓ 找到相关数据: {row}")
                    except Exception as e:
                        print(f"    查询表 {table} 失败: {e}")
                
                conn.close()
            except Exception as e:
                print(f"  打开数据库失败: {e}")

def search_in_files():
    """搜索其他文件中的私钥信息"""
    print("\n=== 搜索其他文件 ===")
    
    search_files = [
        "EpiK_Portal_Restore_Package/app_data/files/exid.dat",
        "EpiK_Portal_Restore_Package/app_data/files/umeng_it.cache",
    ]
    
    for file_path in search_files:
        if Path(file_path).exists():
            print(f"\n检查文件: {file_path}")
            try:
                with open(file_path, 'rb') as f:
                    content = f.read()
                
                # 尝试作为文本读取
                try:
                    text_content = content.decode('utf-8', errors='ignore')
                    if TARGET_ADDRESS.lower() in text_content.lower():
                        print(f"  ✓ 找到目标地址!")
                        print(f"  内容: {text_content[:200]}...")
                except:
                    pass
                
                # 搜索二进制中的私钥模式
                hex_content = binascii.hexlify(content).decode().lower()
                if TARGET_ADDRESS.lower().replace('0x', '') in hex_content:
                    print(f"  ✓ 在十六进制中找到目标地址!")
                
            except Exception as e:
                print(f"  读取文件失败: {e}")

def main():
    """主函数"""
    print("=" * 60)
    print("EpiK Portal 私钥提取工具")
    print("=" * 60)
    print(f"目标地址: {TARGET_ADDRESS}")
    print(f"已知密码: {KNOWN_PASSWORDS}")
    print()
    
    # 1. 提取加密账户数据
    encrypted_data = extract_encrypted_account_data()
    if not encrypted_data:
        print("无法提取加密数据，退出")
        return
    
    # 2. 尝试使用已知密码解密
    print("\n=== 尝试密码解密 ===")
    for password in KNOWN_PASSWORDS:
        result = try_decrypt_with_password(encrypted_data, password)
        if result:
            print(f"\n🎉 解密成功!")
            print(f"使用密码: {password}")
            print(f"解密结果: {result[:500]}...")
            
            # 保存结果
            with open("decrypted_wallet_data.json", "w") as f:
                f.write(result)
            print(f"✓ 解密数据已保存到 decrypted_wallet_data.json")
            return
    
    # 3. 搜索私钥模式
    private_key = search_private_keys_in_data(encrypted_data)
    if private_key:
        print(f"\n🎉 找到私钥: {private_key}")
        
        # 保存私钥
        keystore = {
            "address": TARGET_ADDRESS,
            "private_key": private_key,
            "extraction_method": "pattern_search"
        }
        
        with open("extracted_private_key.json", "w") as f:
            json.dump(keystore, f, indent=2)
        print(f"✓ 私钥已保存到 extracted_private_key.json")
        return
    
    # 4. 搜索数据库
    search_in_databases()
    
    # 5. 搜索其他文件
    search_in_files()
    
    print("\n" + "=" * 60)
    print("搜索完成，未找到私钥")
    print("建议:")
    print("1. 确认密码是否正确")
    print("2. 尝试系统降级方案")
    print("3. 联系EpiK Protocol技术支持")

if __name__ == "__main__":
    main()
