#!/usr/bin/env python3
"""
EpiK Portal 钱包恢复工具
尝试从备份数据中恢复钱包私钥和助记词
"""

import base64
import json
import xml.etree.ElementTree as ET
import sqlite3
import hashlib
import binascii
import re
import os
from pathlib import Path

def analyze_flutter_preferences():
    """分析Flutter SharedPreferences中的钱包数据"""
    print("=== 分析Flutter SharedPreferences ===")
    
    prefs_file = "epik_data_final/apps/com.epik.wallet/sp/FlutterSharedPreferences.xml"
    
    if not Path(prefs_file).exists():
        print(f"文件不存在: {prefs_file}")
        return
    
    tree = ET.parse(prefs_file)
    root = tree.getroot()
    
    wallet_data = {}
    
    for string_elem in root.findall('string'):
        name = string_elem.get('name')
        value = string_elem.text or ""
        
        if 'account' in name.lower() or 'wallet' in name.lower() or 'miner' in name.lower():
            wallet_data[name] = value
            print(f"\n键: {name}")
            print(f"值长度: {len(value)} 字符")
            
            # 尝试解析JSON
            if value.startswith('{') or value.startswith('['):
                try:
                    parsed = json.loads(value.replace('&quot;', '"'))
                    print(f"JSON内容: {json.dumps(parsed, indent=2, ensure_ascii=False)}")
                except:
                    print("无法解析为JSON")
            
            # 检查是否是Base64编码
            if len(value) > 50 and value.replace('+', '').replace('/', '').replace('=', '').isalnum():
                try:
                    decoded = base64.b64decode(value)
                    print(f"Base64解码长度: {len(decoded)} 字节")
                    print(f"Base64解码预览: {decoded[:100]}...")
                except:
                    print("不是有效的Base64")
    
    return wallet_data

def search_for_target_address():
    """搜索目标钱包地址"""
    target_address = "******************************************"
    print(f"\n=== 搜索目标地址: {target_address} ===")
    
    # 搜索所有文本文件
    import os
    import re
    
    found_locations = []
    
    for root, dirs, files in os.walk("epik_data_final"):
        for file in files:
            if file.endswith(('.xml', '.json', '.txt', '.log')):
                filepath = os.path.join(root, file)
                try:
                    with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()
                        if target_address.lower() in content.lower():
                            found_locations.append(filepath)
                            print(f"找到地址在: {filepath}")
                except:
                    pass
    
    return found_locations

def analyze_encrypted_account_data():
    """分析加密的账户数据"""
    print("\n=== 分析加密账户数据 ===")
    
    prefs_file = "epik_data_final/apps/com.epik.wallet/sp/FlutterSharedPreferences.xml"
    tree = ET.parse(prefs_file)
    root = tree.getroot()
    
    for string_elem in root.findall('string'):
        name = string_elem.get('name')
        if name == 'flutter.am_account_list':
            encrypted_data = string_elem.text
            print(f"找到加密账户数据，长度: {len(encrypted_data)} 字符")
            
            # 尝试不同的解码方法
            print("\n尝试Base64解码...")
            try:
                decoded = base64.b64decode(encrypted_data)
                print(f"Base64解码成功，长度: {len(decoded)} 字节")
                
                # 查看前100字节的十六进制
                hex_preview = binascii.hexlify(decoded[:100]).decode()
                print(f"十六进制预览: {hex_preview}")
                
                # 尝试查找可能的钱包地址模式
                decoded_str = decoded.decode('utf-8', errors='ignore')
                print(f"UTF-8解码预览: {decoded_str[:200]}...")
                
                # 搜索0x开头的地址
                addresses = re.findall(r'0x[a-fA-F0-9]{40}', decoded_str)
                if addresses:
                    print(f"找到的地址: {addresses}")
                
                return decoded
                
            except Exception as e:
                print(f"Base64解码失败: {e}")
    
    return None

def analyze_webview_data():
    """分析WebView数据中的钱包信息"""
    print("\n=== 分析WebView数据 ===")
    
    webdata_file = "epik_data_final/apps/com.epik.wallet/r/app_webview/Default/Web Data"
    
    if not Path(webdata_file).exists():
        print("WebView数据文件不存在")
        return
    
    try:
        conn = sqlite3.connect(webdata_file)
        cursor = conn.cursor()
        
        # 查看所有表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        print(f"WebView数据库表: {[t[0] for t in tables]}")
        
        # 查找可能包含钱包信息的表
        for table_name in ['autofill_profiles', 'autofill_profile_names', 'meta']:
            try:
                cursor.execute(f"SELECT * FROM {table_name[0]} LIMIT 5;")
                rows = cursor.fetchall()
                if rows:
                    print(f"\n表 {table_name[0]} 的内容:")
                    for row in rows:
                        print(row)
            except:
                pass
        
        conn.close()
        
    except Exception as e:
        print(f"分析WebView数据失败: {e}")

def extract_possible_keys():
    """提取可能的私钥和助记词"""
    print("\n=== 提取可能的私钥和助记词 ===")
    
    # 常见的助记词单词列表（BIP39）
    common_words = ['abandon', 'ability', 'able', 'about', 'above', 'absent', 'absorb', 'abstract', 'absurd', 'abuse']
    
    # 搜索所有文件中的可能私钥格式
    import os
    import re
    
    private_key_patterns = [
        r'[a-fA-F0-9]{64}',  # 64位十六进制私钥
        r'0x[a-fA-F0-9]{64}',  # 带0x前缀的私钥
    ]
    
    mnemonic_patterns = [
        r'\b(?:' + '|'.join(common_words) + r')\b(?:\s+\b(?:' + '|'.join(common_words) + r')\b){11,23}',  # 12-24个助记词
    ]
    
    found_keys = []
    found_mnemonics = []
    
    for root, dirs, files in os.walk("epik_data_final"):
        for file in files:
            if file.endswith(('.xml', '.json', '.txt', '.log')):
                filepath = os.path.join(root, file)
                try:
                    with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()
                        
                        # 搜索私钥
                        for pattern in private_key_patterns:
                            matches = re.findall(pattern, content)
                            for match in matches:
                                if match not in found_keys:
                                    found_keys.append((match, filepath))
                        
                        # 搜索助记词
                        for pattern in mnemonic_patterns:
                            matches = re.findall(pattern, content, re.IGNORECASE)
                            for match in matches:
                                if match not in found_mnemonics:
                                    found_mnemonics.append((match, filepath))
                                    
                except:
                    pass
    
    print(f"找到 {len(found_keys)} 个可能的私钥:")
    for key, filepath in found_keys:
        print(f"  {key} (来源: {filepath})")
    
    print(f"\n找到 {len(found_mnemonics)} 个可能的助记词:")
    for mnemonic, filepath in found_mnemonics:
        print(f"  {mnemonic} (来源: {filepath})")
    
    return found_keys, found_mnemonics

def main():
    """主函数"""
    print("EpiK Portal 钱包恢复工具")
    print("=" * 50)
    
    # 1. 分析Flutter偏好设置
    wallet_data = analyze_flutter_preferences()
    
    # 2. 搜索目标地址
    locations = search_for_target_address()
    
    # 3. 分析加密账户数据
    encrypted_data = analyze_encrypted_account_data()
    
    # 4. 分析WebView数据
    analyze_webview_data()
    
    # 5. 提取可能的私钥和助记词
    keys, mnemonics = extract_possible_keys()
    
    print("\n" + "=" * 50)
    print("分析完成！")
    print(f"目标地址: ******************************************")
    print("请检查上述输出中的加密数据和可能的私钥信息。")

if __name__ == "__main__":
    main()
