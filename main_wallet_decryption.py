#!/usr/bin/env python3
"""
主钱包解密工具
专门针对主钱包地址使用钱包密码进行解密
"""

import base64
import json
import xml.etree.ElementTree as ET
import hashlib
import binascii
import re
import os
from ecdsa import SigningKey, SECP256k1
from Crypto.Hash import keccak
from Crypto.Cipher import AES
from Crypto.Protocol.KDF import PBKDF2, scrypt
import hmac

TARGET_ADDRESS = "******************************************"
WALLET_PASSWORDS = ["zyuu23521", "zy96669", "2221237"]

def private_key_to_address(private_key_hex):
    """从私钥生成以太坊地址"""
    try:
        private_key_bytes = binascii.unhexlify(private_key_hex)
        sk = SigningKey.from_string(private_key_bytes, curve=SECP256k1)
        public_key = sk.get_verifying_key().to_string()
        
        keccak_hash = keccak.new(digest_bits=256)
        keccak_hash.update(public_key)
        address_bytes = keccak_hash.digest()[-20:]
        
        address = "0x" + binascii.hexlify(address_bytes).decode().lower()
        return address
    except:
        return None

def extract_main_wallet_data():
    """提取主钱包的加密数据"""
    print("=== 提取主钱包加密数据 ===")
    
    prefs_file = "epik_data_final/apps/com.epik.wallet/sp/FlutterSharedPreferences.xml"
    tree = ET.parse(prefs_file)
    root = tree.getroot()
    
    for string_elem in root.findall('string'):
        name = string_elem.get('name')
        if name == 'flutter.am_account_list':
            encrypted_b64 = string_elem.text
            encrypted_data = base64.b64decode(encrypted_b64)
            
            print(f"主钱包加密数据长度: {len(encrypted_data)} 字节")
            print(f"数据预览: {binascii.hexlify(encrypted_data[:32]).decode()}...")
            
            return encrypted_data
    
    return None

def try_wallet_password_decryption(encrypted_data, password):
    """使用钱包密码尝试解密"""
    print(f"\n{'='*60}")
    print(f"尝试钱包密码: {password}")
    print(f"{'='*60}")
    
    # 方法1: PBKDF2 + AES (最常见的钱包加密方式)
    print("1. 尝试 PBKDF2 + AES 解密...")
    
    # 常见的盐值和迭代次数组合
    pbkdf2_configs = [
        (b"", 1000),           # 无盐，1000次迭代
        (b"epik", 1000),       # epik作为盐
        (b"EpiK", 1000),       # EpiK作为盐
        (b"wallet", 1000),     # wallet作为盐
        (b"com.epik.wallet", 1000),  # 包名作为盐
        (password.encode(), 1000),    # 密码本身作为盐
        (b"", 4096),           # 无盐，4096次迭代
        (b"epik", 4096),       # epik作为盐，4096次迭代
        (b"", 10000),          # 无盐，10000次迭代
        (TARGET_ADDRESS[2:].encode(), 1000),  # 地址作为盐
    ]
    
    for salt, iterations in pbkdf2_configs:
        try:
            # 生成密钥
            key = PBKDF2(password, salt, 32, count=iterations)
            
            # AES-ECB解密
            if len(encrypted_data) % 16 == 0:
                cipher = AES.new(key, AES.MODE_ECB)
                decrypted = cipher.decrypt(encrypted_data)
                result = analyze_decrypted_wallet_data(decrypted, f"PBKDF2({iterations})-{salt}-AES-ECB", password)
                if result:
                    return result
            
            # AES-CBC解密 (假设前16字节是IV)
            if len(encrypted_data) >= 32 and len(encrypted_data) % 16 == 0:
                iv = encrypted_data[:16]
                ciphertext = encrypted_data[16:]
                cipher = AES.new(key, AES.MODE_CBC, iv)
                decrypted = cipher.decrypt(ciphertext)
                result = analyze_decrypted_wallet_data(decrypted, f"PBKDF2({iterations})-{salt}-AES-CBC", password)
                if result:
                    return result
            
            # AES-CBC解密 (使用固定IV)
            if len(encrypted_data) % 16 == 0:
                iv = b'\x00' * 16  # 全零IV
                cipher = AES.new(key, AES.MODE_CBC, iv)
                decrypted = cipher.decrypt(encrypted_data)
                result = analyze_decrypted_wallet_data(decrypted, f"PBKDF2({iterations})-{salt}-AES-CBC-ZeroIV", password)
                if result:
                    return result
                    
        except Exception as e:
            continue
    
    # 方法2: 简单哈希 + AES
    print("\n2. 尝试简单哈希 + AES 解密...")
    
    hash_methods = [
        ("SHA256", hashlib.sha256),
        ("MD5", hashlib.md5),
        ("SHA1", hashlib.sha1),
        ("SHA512", lambda x: hashlib.sha512(x).digest()[:32]),
    ]
    
    for method_name, hash_func in hash_methods:
        try:
            # 直接哈希密码
            key = hash_func(password.encode()).digest()[:32]
            
            # AES-ECB
            if len(encrypted_data) % 16 == 0:
                cipher = AES.new(key, AES.MODE_ECB)
                decrypted = cipher.decrypt(encrypted_data)
                result = analyze_decrypted_wallet_data(decrypted, f"{method_name}-AES-ECB", password)
                if result:
                    return result
            
            # 密码+地址组合哈希
            combined = password + TARGET_ADDRESS
            key2 = hash_func(combined.encode()).digest()[:32]
            
            if len(encrypted_data) % 16 == 0:
                cipher = AES.new(key2, AES.MODE_ECB)
                decrypted = cipher.decrypt(encrypted_data)
                result = analyze_decrypted_wallet_data(decrypted, f"{method_name}(pwd+addr)-AES-ECB", password)
                if result:
                    return result
                    
        except Exception as e:
            continue
    
    # 方法3: Scrypt + AES (一些钱包使用)
    print("\n3. 尝试 Scrypt + AES 解密...")
    
    try:
        # 标准scrypt参数
        key = scrypt(password.encode(), b"epik", 32, N=2**14, r=8, p=1)
        
        if len(encrypted_data) % 16 == 0:
            cipher = AES.new(key, AES.MODE_ECB)
            decrypted = cipher.decrypt(encrypted_data)
            result = analyze_decrypted_wallet_data(decrypted, "Scrypt-AES-ECB", password)
            if result:
                return result
    except:
        pass
    
    # 方法4: 基于已知信息的密钥派生
    print("\n4. 尝试基于已知信息的密钥派生...")
    
    known_info = [
        "f042571",      # 主矿工ID
        "293794365",    # 主账户ID
        "1629169488944", # 二维码生成时间戳
    ]
    
    for info in known_info:
        try:
            # 密码 + 已知信息
            combined = password + info
            key = hashlib.sha256(combined.encode()).digest()
            
            if len(encrypted_data) % 16 == 0:
                cipher = AES.new(key, AES.MODE_ECB)
                decrypted = cipher.decrypt(encrypted_data)
                result = analyze_decrypted_wallet_data(decrypted, f"SHA256(pwd+{info})-AES", password)
                if result:
                    return result
        except:
            continue
    
    return None

def analyze_decrypted_wallet_data(data, method, password):
    """分析解密后的钱包数据"""
    try:
        # 检查数据是否合理
        if not is_reasonable_decryption(data):
            return None
        
        print(f"\n*** {method}: 解密成功，数据看起来合理 ***")
        
        # 尝试UTF-8解码
        try:
            text = data.decode('utf-8', errors='ignore')
            
            # 检查是否包含目标地址
            if TARGET_ADDRESS.lower() in text.lower():
                print(f"*** 在解密数据中找到目标地址！***")
                print(f"解密文本预览: {text[:300]}...")
                
                # 搜索私钥
                private_keys = re.findall(r'[a-fA-F0-9]{64}', text)
                for key in private_keys:
                    address = private_key_to_address(key)
                    if address and address.lower() == TARGET_ADDRESS.lower():
                        print(f"*** 找到目标地址的私钥: {key} ***")
                        return key
            
            # 检查是否是JSON格式
            if text.strip().startswith('{') or text.strip().startswith('['):
                try:
                    json_data = json.loads(text)
                    print(f"解密出JSON数据:")
                    print(json.dumps(json_data, indent=2, ensure_ascii=False)[:500])
                    
                    result = search_json_for_main_wallet_key(json_data)
                    if result:
                        return result
                except:
                    pass
            
            # 检查是否包含钱包关键词
            wallet_keywords = ['private', 'key', 'mnemonic', 'seed', 'account', 'wallet', 'address']
            if any(keyword in text.lower() for keyword in wallet_keywords):
                print(f"包含钱包关键词，文本预览: {text[:300]}...")
                
                # 搜索所有64位十六进制字符串
                private_keys = re.findall(r'[a-fA-F0-9]{64}', text)
                for key in private_keys:
                    address = private_key_to_address(key)
                    if address and address.lower() == TARGET_ADDRESS.lower():
                        print(f"*** 找到目标地址的私钥: {key} ***")
                        return key
        except:
            pass
        
        # 搜索二进制数据中的私钥
        for i in range(len(data) - 31):
            candidate = binascii.hexlify(data[i:i+32]).decode()
            if len(candidate) == 64 and candidate != "0" * 64:
                address = private_key_to_address(candidate)
                if address and address.lower() == TARGET_ADDRESS.lower():
                    print(f"*** 在二进制数据中找到私钥: {candidate} ***")
                    return candidate
        
    except:
        pass
    
    return None

def is_reasonable_decryption(data):
    """检查解密结果是否合理"""
    if len(data) == 0:
        return False
    
    # 检查是否全是相同字节
    if len(set(data)) == 1:
        return False
    
    # 检查是否有合理的字节分布
    try:
        text = data.decode('utf-8', errors='ignore')
        printable_ratio = sum(1 for c in text if c.isprintable()) / len(text)
        if printable_ratio > 0.3:  # 至少30%可打印字符
            return True
    except:
        pass
    
    # 检查是否包含常见的数据模式
    hex_data = binascii.hexlify(data).decode()
    if any(pattern in hex_data for pattern in ['7b', '22', '5b', '7d']):  # JSON相关字符
        return True
    
    return False

def search_json_for_main_wallet_key(json_data):
    """在JSON数据中搜索主钱包私钥"""
    def search_recursive(obj, path=""):
        if isinstance(obj, dict):
            for key, value in obj.items():
                current_path = f"{path}.{key}" if path else key
                
                if isinstance(value, str):
                    # 检查是否是私钥
                    if len(value) == 64 and re.match(r'^[a-fA-F0-9]{64}$', value):
                        address = private_key_to_address(value)
                        if address and address.lower() == TARGET_ADDRESS.lower():
                            print(f"*** 在JSON路径 {current_path} 找到主钱包私钥: {value} ***")
                            return value
                    
                    # 检查是否包含目标地址
                    if value.lower() == TARGET_ADDRESS.lower():
                        print(f"在JSON路径 {current_path} 找到目标地址")
                
                result = search_recursive(value, current_path)
                if result:
                    return result
                    
        elif isinstance(obj, list):
            for i, item in enumerate(obj):
                result = search_recursive(item, f"{path}[{i}]")
                if result:
                    return result
        
        return None
    
    return search_recursive(json_data)

def main():
    """主函数"""
    print("EpiK Portal 主钱包解密工具")
    print(f"目标主钱包地址: {TARGET_ADDRESS}")
    print(f"尝试钱包密码: {', '.join(WALLET_PASSWORDS)}")
    print("基于区块链交易记录确认这是真实的主钱包地址")
    print("=" * 80)
    
    # 提取主钱包加密数据
    encrypted_data = extract_main_wallet_data()
    if not encrypted_data:
        print("未找到主钱包加密数据")
        return
    
    # 尝试每个钱包密码
    for password in WALLET_PASSWORDS:
        result = try_wallet_password_decryption(encrypted_data, password)
        if result:
            print(f"\n" + "=" * 80)
            print("*** 主钱包解密成功！***")
            print(f"私钥: {result}")
            print(f"密码: {password}")
            print(f"地址: {private_key_to_address(result)}")
            print("=" * 80)
            
            # 保存结果
            with open("main_wallet_private_key.json", "w") as f:
                json.dump({
                    "success": True,
                    "main_wallet_address": TARGET_ADDRESS,
                    "private_key": result,
                    "password_used": password,
                    "verification_address": private_key_to_address(result),
                    "blockchain_confirmed": True
                }, f, indent=2)
            
            print("主钱包私钥已保存到: main_wallet_private_key.json")
            return result
    
    print(f"\n" + "=" * 80)
    print("所有钱包密码尝试完毕，未能成功解密主钱包")
    print("可能需要:")
    print("1. 确认钱包密码是否正确")
    print("2. 检查是否使用了不同的加密算法")
    print("3. 可能需要额外的解密参数")
    print("=" * 80)

if __name__ == "__main__":
    main()
