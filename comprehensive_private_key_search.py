#!/usr/bin/env python3
"""
全面的私钥搜索工具
尝试所有可能的32字节序列和解密方法
"""

import base64
import binascii
import json
import xml.etree.ElementTree as ET
import hashlib
import struct
from pathlib import Path

TARGET_ADDRESS = "******************************************"
KNOWN_PASSWORDS = ["zyuu23521", "123456", "password", "epik", "portal", "wallet"]

def extract_encrypted_data():
    """提取加密数据"""
    prefs_file = "EpiK_Portal_Restore_Package/app_data/shared_prefs/FlutterSharedPreferences.xml"
    tree = ET.parse(prefs_file)
    root = tree.getroot()
    
    for string_elem in root.findall('string'):
        name = string_elem.get('name')
        if name == 'flutter.am_account_list':
            encrypted_b64 = string_elem.text
            encrypted_data = base64.b64decode(encrypted_b64)
            return encrypted_data
    return None

def try_all_decryption_methods(encrypted_data):
    """尝试所有可能的解密方法"""
    print("=== 尝试所有解密方法 ===")
    
    results = []
    
    for password in KNOWN_PASSWORDS:
        print(f"\n使用密码: {password}")
        
        # 方法1: 简单XOR
        result = try_xor_decryption(encrypted_data, password)
        if result:
            results.append(("XOR", password, result))
        
        # 方法2: 密码哈希XOR
        for hash_name in ['md5', 'sha1', 'sha256', 'sha512']:
            result = try_hash_xor_decryption(encrypted_data, password, hash_name)
            if result:
                results.append((f"Hash-XOR-{hash_name}", password, result))
        
        # 方法3: PBKDF2派生密钥
        result = try_pbkdf2_decryption(encrypted_data, password)
        if result:
            results.append(("PBKDF2", password, result))
        
        # 方法4: 简单偏移
        for offset in [0, 1, 2, 4, 8, 16]:
            result = try_offset_decryption(encrypted_data, password, offset)
            if result:
                results.append((f"Offset-{offset}", password, result))
    
    return results

def try_xor_decryption(encrypted_data, password):
    """简单XOR解密"""
    password_bytes = password.encode('utf-8')
    
    decrypted = bytearray()
    for i, byte in enumerate(encrypted_data):
        decrypted.append(byte ^ password_bytes[i % len(password_bytes)])
    
    return search_private_keys_in_data(decrypted, f"XOR-{password}")

def try_hash_xor_decryption(encrypted_data, password, hash_name):
    """使用密码哈希进行XOR解密"""
    hash_func = getattr(hashlib, hash_name)
    key = hash_func(password.encode()).digest()
    
    decrypted = bytearray()
    for i, byte in enumerate(encrypted_data):
        decrypted.append(byte ^ key[i % len(key)])
    
    return search_private_keys_in_data(decrypted, f"Hash-XOR-{hash_name}-{password}")

def try_pbkdf2_decryption(encrypted_data, password):
    """使用PBKDF2派生密钥进行XOR解密"""
    # 尝试不同的盐值
    salts = [b'', b'salt', b'flutter', b'epik', password.encode()]
    
    for salt in salts:
        key = hashlib.pbkdf2_hmac('sha256', password.encode(), salt, 10000, 32)
        
        decrypted = bytearray()
        for i, byte in enumerate(encrypted_data):
            decrypted.append(byte ^ key[i % len(key)])
        
        result = search_private_keys_in_data(decrypted, f"PBKDF2-{password}-{salt}")
        if result:
            return result
    
    return None

def try_offset_decryption(encrypted_data, password, offset):
    """尝试偏移解密"""
    if offset >= len(encrypted_data):
        return None
    
    shifted_data = encrypted_data[offset:] + encrypted_data[:offset]
    return try_xor_decryption(shifted_data, password)

def search_private_keys_in_data(data, method_name):
    """在解密数据中搜索私钥"""
    found_keys = []
    
    # 搜索所有32字节序列
    for i in range(len(data) - 31):
        candidate = data[i:i+32]
        
        if is_potential_private_key(candidate):
            hex_key = binascii.hexlify(candidate).decode()
            
            # 验证私钥
            if verify_private_key_simple(hex_key):
                found_keys.append((i, hex_key))
                print(f"  ✓ 找到潜在私钥 (方法: {method_name}, 偏移: {i}): {hex_key}")
    
    return found_keys if found_keys else None

def is_potential_private_key(key_bytes):
    """检查是否可能是私钥"""
    if len(key_bytes) != 32:
        return False
    
    # 不应该全为0或全为FF
    if key_bytes == b'\x00' * 32 or key_bytes == b'\xff' * 32:
        return False
    
    # 应该有一定的随机性
    unique_bytes = len(set(key_bytes))
    if unique_bytes < 8:
        return False
    
    # 检查是否在secp256k1的有效范围内
    key_int = int.from_bytes(key_bytes, 'big')
    secp256k1_n = 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD0364141
    
    if key_int <= 0 or key_int >= secp256k1_n:
        return False
    
    return True

def verify_private_key_simple(private_key_hex):
    """简单验证私钥格式"""
    try:
        # 检查长度和格式
        if len(private_key_hex) != 64:
            return False
        
        # 检查是否为有效的十六进制
        int(private_key_hex, 16)
        
        return True
    except:
        return False

def brute_force_search_all_positions(encrypted_data):
    """暴力搜索所有位置的32字节序列"""
    print("\n=== 暴力搜索所有32字节序列 ===")
    
    candidates = []
    
    for i in range(len(encrypted_data) - 31):
        candidate = encrypted_data[i:i+32]
        
        if is_potential_private_key(candidate):
            hex_key = binascii.hexlify(candidate).decode()
            candidates.append((i, hex_key))
    
    print(f"找到 {len(candidates)} 个潜在的私钥候选")
    
    # 保存所有候选到文件
    with open("all_private_key_candidates.txt", "w") as f:
        f.write("偏移量,私钥\n")
        for offset, hex_key in candidates:
            f.write(f"{offset:03x},{hex_key}\n")
    
    print("✓ 所有候选已保存到 all_private_key_candidates.txt")
    
    return candidates

def try_ethereum_address_derivation(private_key_hex):
    """尝试从私钥推导以太坊地址"""
    try:
        # 这里需要实际的椭圆曲线计算
        # 由于复杂性，我们先返回False，实际使用时需要完整实现
        return False
    except:
        return False

def analyze_data_patterns(encrypted_data):
    """分析数据模式"""
    print("\n=== 数据模式分析 ===")
    
    # 查找重复的字节序列
    patterns = {}
    for length in [4, 8, 16, 32]:
        print(f"\n{length}字节模式:")
        pattern_count = {}
        
        for i in range(len(encrypted_data) - length + 1):
            pattern = encrypted_data[i:i+length]
            pattern_hex = binascii.hexlify(pattern).decode()
            
            if pattern_hex in pattern_count:
                pattern_count[pattern_hex] += 1
            else:
                pattern_count[pattern_hex] = 1
        
        # 显示重复的模式
        repeated = {k: v for k, v in pattern_count.items() if v > 1}
        if repeated:
            print(f"  重复模式: {len(repeated)} 个")
            for pattern, count in sorted(repeated.items(), key=lambda x: x[1], reverse=True)[:5]:
                print(f"    {pattern}: {count} 次")
        else:
            print(f"  无重复模式")

def main():
    """主函数"""
    print("=" * 60)
    print("全面私钥搜索工具")
    print("=" * 60)
    print(f"目标地址: {TARGET_ADDRESS}")
    
    # 提取加密数据
    encrypted_data = extract_encrypted_data()
    if not encrypted_data:
        print("无法提取加密数据")
        return
    
    print(f"加密数据长度: {len(encrypted_data)} 字节")
    
    # 分析数据模式
    analyze_data_patterns(encrypted_data)
    
    # 尝试所有解密方法
    decryption_results = try_all_decryption_methods(encrypted_data)
    
    if decryption_results:
        print(f"\n🎉 找到 {len(decryption_results)} 个可能的解密结果!")
        
        for method, password, keys in decryption_results:
            print(f"\n方法: {method}, 密码: {password}")
            for offset, hex_key in keys:
                print(f"  偏移 {offset:03x}: {hex_key}")
                
                # 保存找到的私钥
                keystore = {
                    "address": TARGET_ADDRESS,
                    "private_key": hex_key,
                    "extraction_method": method,
                    "password": password,
                    "offset": offset
                }
                
                filename = f"potential_private_key_{method}_{password}_{offset}.json"
                with open(filename, "w") as f:
                    json.dump(keystore, f, indent=2)
                
                print(f"    ✓ 保存到 {filename}")
    else:
        print("\n未通过解密方法找到私钥")
    
    # 暴力搜索所有位置
    all_candidates = brute_force_search_all_positions(encrypted_data)
    
    print(f"\n" + "=" * 60)
    print("搜索完成")
    print(f"解密结果: {len(decryption_results) if decryption_results else 0}")
    print(f"暴力搜索候选: {len(all_candidates)}")
    
    if decryption_results or all_candidates:
        print("\n下一步建议:")
        print("1. 使用专业的以太坊工具验证找到的私钥")
        print("2. 检查生成的JSON文件")
        print("3. 尝试导入私钥到钱包软件")
    else:
        print("\n建议:")
        print("1. 尝试更多密码组合")
        print("2. 分析EpiK Portal的源代码")
        print("3. 考虑系统降级方案")

if __name__ == "__main__":
    main()
