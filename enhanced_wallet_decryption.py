#!/usr/bin/env python3
"""
增强版钱包解密工具
基于Flutter钱包加密的通用实现方式
结合EpiK Protocol的特定实现
"""

import base64
import json
import xml.etree.ElementTree as ET
import hashlib
import binascii
import hmac
import os
from ecdsa import SigningKey, SECP256k1
from Crypto.Hash import keccak
from Crypto.Cipher import AES
from Crypto.Protocol.KDF import PBKDF2, scrypt
from Crypto.Util.Padding import unpad
import struct

TARGET_ADDRESS = "******************************************"
WALLET_PASSWORDS = ["zyuu23521", "zy96669", "2221237"]

def private_key_to_address(private_key_hex):
    """从私钥生成以太坊地址"""
    try:
        private_key_bytes = binascii.unhexlify(private_key_hex)
        sk = SigningKey.from_string(private_key_bytes, curve=SECP256k1)
        public_key = sk.get_verifying_key().to_string()
        
        keccak_hash = keccak.new(digest_bits=256)
        keccak_hash.update(public_key)
        address_bytes = keccak_hash.digest()[-20:]
        
        address = "0x" + binascii.hexlify(address_bytes).decode().lower()
        return address
    except:
        return None

def extract_encrypted_data():
    """提取加密的账户数据"""
    prefs_file = "epik_data_final/apps/com.epik.wallet/sp/FlutterSharedPreferences.xml"
    tree = ET.parse(prefs_file)
    root = tree.getroot()
    
    for string_elem in root.findall('string'):
        name = string_elem.get('name')
        if name == 'flutter.am_account_list':
            encrypted_b64 = string_elem.text
            encrypted_data = base64.b64decode(encrypted_b64)
            print(f"提取到加密数据: {len(encrypted_data)} 字节")
            return encrypted_data
    
    return None

def try_flutter_standard_decryption(encrypted_data, password):
    """尝试Flutter标准加密方式解密"""
    results = []
    
    # 方法1: PBKDF2 + AES-256-CBC (Flutter常用)
    try:
        # 提取盐值 (通常前16字节)
        if len(encrypted_data) > 16:
            salt = encrypted_data[:16]
            ciphertext = encrypted_data[16:]
            
            # PBKDF2密钥派生
            key = PBKDF2(password, salt, 32, count=10000)
            
            # AES-256-CBC解密
            if len(ciphertext) >= 16:
                iv = ciphertext[:16]
                encrypted_content = ciphertext[16:]
                
                cipher = AES.new(key, AES.MODE_CBC, iv)
                decrypted = cipher.decrypt(encrypted_content)
                
                # 去除PKCS7填充
                try:
                    decrypted = unpad(decrypted, AES.block_size)
                    results.append(("PBKDF2-AES-CBC", decrypted))
                except:
                    pass
    except Exception as e:
        pass
    
    # 方法2: PBKDF2 + AES-256-ECB
    try:
        salt = encrypted_data[:16]
        ciphertext = encrypted_data[16:]
        
        key = PBKDF2(password, salt, 32, count=10000)
        cipher = AES.new(key, AES.MODE_ECB)
        decrypted = cipher.decrypt(ciphertext)
        
        try:
            decrypted = unpad(decrypted, AES.block_size)
            results.append(("PBKDF2-AES-ECB", decrypted))
        except:
            pass
    except:
        pass
    
    # 方法3: Scrypt + AES (现代Flutter应用常用)
    try:
        salt = encrypted_data[:32]  # Scrypt通常使用32字节盐
        ciphertext = encrypted_data[32:]
        
        key = scrypt(password.encode(), salt, 32, N=16384, r=8, p=1)
        
        if len(ciphertext) >= 16:
            iv = ciphertext[:16]
            encrypted_content = ciphertext[16:]
            
            cipher = AES.new(key, AES.MODE_CBC, iv)
            decrypted = cipher.decrypt(encrypted_content)
            
            try:
                decrypted = unpad(decrypted, AES.block_size)
                results.append(("Scrypt-AES-CBC", decrypted))
            except:
                pass
    except:
        pass
    
    # 方法4: 简单密钥派生 + AES
    try:
        # 使用密码的SHA256作为密钥
        key = hashlib.sha256(password.encode()).digest()
        
        if len(encrypted_data) >= 16:
            iv = encrypted_data[:16]
            ciphertext = encrypted_data[16:]
            
            cipher = AES.new(key, AES.MODE_CBC, iv)
            decrypted = cipher.decrypt(ciphertext)
            
            try:
                decrypted = unpad(decrypted, AES.block_size)
                results.append(("SHA256-AES-CBC", decrypted))
            except:
                pass
    except:
        pass
    
    return results

def try_epik_specific_decryption(encrypted_data, password):
    """尝试EpiK Protocol特定的解密方式"""
    results = []
    
    # 基于发现的矿工ID和时间戳的密钥派生
    known_values = [
        "f042571",  # 矿工ID
        "293794365",  # 用户ID
        "1629169488944",  # 时间戳
    ]
    
    for value in known_values:
        try:
            # 方法1: 密码 + 已知值的组合
            combined_key = password + value
            key = hashlib.sha256(combined_key.encode()).digest()
            
            if len(encrypted_data) >= 16:
                iv = encrypted_data[:16]
                ciphertext = encrypted_data[16:]
                
                cipher = AES.new(key, AES.MODE_CBC, iv)
                decrypted = cipher.decrypt(ciphertext)
                
                try:
                    decrypted = unpad(decrypted, AES.block_size)
                    results.append((f"SHA256({password}+{value})-AES", decrypted))
                except:
                    pass
        except:
            pass
        
        try:
            # 方法2: HMAC-SHA256密钥派生
            key = hmac.new(password.encode(), value.encode(), hashlib.sha256).digest()
            
            if len(encrypted_data) >= 16:
                iv = encrypted_data[:16]
                ciphertext = encrypted_data[16:]
                
                cipher = AES.new(key, AES.MODE_CBC, iv)
                decrypted = cipher.decrypt(ciphertext)
                
                try:
                    decrypted = unpad(decrypted, AES.block_size)
                    results.append((f"HMAC-SHA256({password},{value})-AES", decrypted))
                except:
                    pass
        except:
            pass
    
    return results

def analyze_decrypted_data(data, method_name):
    """分析解密后的数据"""
    try:
        # 尝试解析为JSON
        if data.startswith(b'{') or data.startswith(b'['):
            json_data = json.loads(data.decode('utf-8'))
            print(f"\n*** {method_name}: JSON解析成功 ***")
            print(f"JSON结构: {type(json_data)}")
            
            if isinstance(json_data, dict):
                print(f"JSON键: {list(json_data.keys())}")
            elif isinstance(json_data, list):
                print(f"JSON数组长度: {len(json_data)}")
            
            # 搜索私钥模式
            json_str = json.dumps(json_data)
            if TARGET_ADDRESS.lower() in json_str.lower():
                print(f"🎯 找到目标地址!")
                return True, json_data
            
            # 搜索可能的私钥
            private_key_patterns = []
            import re
            hex_64_pattern = re.findall(r'[0-9a-fA-F]{64}', json_str)
            for pattern in hex_64_pattern:
                address = private_key_to_address(pattern)
                if address:
                    private_key_patterns.append((pattern, address))
                    if address.lower() == TARGET_ADDRESS.lower():
                        print(f"🎯 找到目标私钥: {pattern}")
                        return True, {"private_key": pattern, "address": address}
            
            if private_key_patterns:
                print(f"发现 {len(private_key_patterns)} 个可能的私钥")
                for pk, addr in private_key_patterns:
                    print(f"  私钥: {pk[:16]}... -> 地址: {addr}")
            
            return False, json_data
        
        # 尝试解析为文本
        try:
            text = data.decode('utf-8')
            if len(text) > 10 and all(c.isprintable() or c.isspace() for c in text):
                print(f"\n*** {method_name}: 文本解析成功 ***")
                print(f"文本长度: {len(text)}")
                print(f"文本预览: {text[:200]}...")
                
                if TARGET_ADDRESS.lower() in text.lower():
                    print(f"🎯 找到目标地址!")
                    return True, text
                
                return False, text
        except:
            pass
        
        # 二进制数据分析
        print(f"\n*** {method_name}: 二进制数据 ***")
        print(f"数据长度: {len(data)} 字节")
        print(f"十六进制: {binascii.hexlify(data)[:100].decode()}...")
        
        # 搜索32字节的私钥候选
        if len(data) >= 32:
            for i in range(len(data) - 31):
                candidate = data[i:i+32]
                candidate_hex = binascii.hexlify(candidate).decode()
                address = private_key_to_address(candidate_hex)
                if address and address.lower() == TARGET_ADDRESS.lower():
                    print(f"🎯 在偏移 {i} 找到目标私钥: {candidate_hex}")
                    return True, {"private_key": candidate_hex, "address": address}
        
        return False, data
        
    except Exception as e:
        print(f"分析数据时出错: {e}")
        return False, None

def main():
    """主函数"""
    print("=" * 80)
    print("增强版EpiK钱包解密工具")
    print("=" * 80)
    
    # 提取加密数据
    encrypted_data = extract_encrypted_data()
    if not encrypted_data:
        print("错误: 无法提取加密数据")
        return
    
    print(f"加密数据长度: {len(encrypted_data)} 字节")
    print(f"数据十六进制: {binascii.hexlify(encrypted_data)[:100].decode()}...")
    
    success_results = []
    
    # 对每个密码尝试不同的解密方法
    for password in WALLET_PASSWORDS:
        print(f"\n{'='*60}")
        print(f"尝试密码: {password}")
        print(f"{'='*60}")
        
        # Flutter标准解密方法
        flutter_results = try_flutter_standard_decryption(encrypted_data, password)
        for method, decrypted_data in flutter_results:
            found_target, parsed_data = analyze_decrypted_data(decrypted_data, f"Flutter-{method}")
            if found_target:
                success_results.append({
                    "password": password,
                    "method": f"Flutter-{method}",
                    "data": parsed_data
                })
        
        # EpiK特定解密方法
        epik_results = try_epik_specific_decryption(encrypted_data, password)
        for method, decrypted_data in epik_results:
            found_target, parsed_data = analyze_decrypted_data(decrypted_data, f"EpiK-{method}")
            if found_target:
                success_results.append({
                    "password": password,
                    "method": f"EpiK-{method}",
                    "data": parsed_data
                })
    
    # 输出结果
    print(f"\n{'='*80}")
    if success_results:
        print("🎉 解密成功!")
        for result in success_results:
            print(f"密码: {result['password']}")
            print(f"方法: {result['method']}")
            print(f"数据: {result['data']}")
            
            # 保存结果
            with open("successful_decryption_results.json", "w") as f:
                json.dump(success_results, f, indent=2)
            print("结果已保存到: successful_decryption_results.json")
    else:
        print("❌ 所有解密尝试都失败了")
        print("\n可能的原因:")
        print("1. 密码不正确")
        print("2. EpiK使用了自定义的加密算法")
        print("3. 需要额外的解密参数")
        print("\n建议:")
        print("1. 确认钱包密码是否正确")
        print("2. 联系EpiK Protocol技术支持")
        print("3. 寻找EpiK Portal的源代码")
    print(f"{'='*80}")

if __name__ == "__main__":
    main()
