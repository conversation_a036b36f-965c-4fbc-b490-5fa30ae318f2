#!/usr/bin/env python3
"""
综合解密攻击工具
采用逆向工程方法，系统性尝试各种可能的加密方案
"""

import base64
import json
import xml.etree.ElementTree as ET
import hashlib
import binascii
import hmac
import os
import struct
import itertools
from ecdsa import SigningKey, SECP256k1
from Crypto.Hash import keccak
from Crypto.Cipher import AES, DES3, Blowfish
from Crypto.Protocol.KDF import PBKDF2, scrypt
from Crypto.Util.Padding import unpad
from Crypto.Random import get_random_bytes
# import argon2  # 暂时注释掉，避免依赖问题

TARGET_ADDRESS = "******************************************"
WALLET_PASSWORDS = ["zyuu23521", "zy96669", "2221237"]

# 从数据中提取的已知值
KNOWN_VALUES = {
    "miner_id": "f042571",
    "user_id": "293794365", 
    "timestamp": "1629169488944",
    "tx_hash": "0xb81331959804b20cfba56438ad8a6f1db7215a211b755cc02927c7db5db4443a",
    "app_version": "1.1.2",
    "device_info": "926adc0f9053f6db"
}

def private_key_to_address(private_key_hex):
    """从私钥生成以太坊地址"""
    try:
        if len(private_key_hex) != 64:
            return None
        private_key_bytes = binascii.unhexlify(private_key_hex)
        sk = SigningKey.from_string(private_key_bytes, curve=SECP256k1)
        public_key = sk.get_verifying_key().to_string()
        
        keccak_hash = keccak.new(digest_bits=256)
        keccak_hash.update(public_key)
        address_bytes = keccak_hash.digest()[-20:]
        
        address = "0x" + binascii.hexlify(address_bytes).decode().lower()
        return address
    except:
        return None

def extract_encrypted_data():
    """提取加密数据"""
    prefs_file = "epik_data_final/apps/com.epik.wallet/sp/FlutterSharedPreferences.xml"
    tree = ET.parse(prefs_file)
    root = tree.getroot()
    
    for string_elem in root.findall('string'):
        name = string_elem.get('name')
        if name == 'flutter.am_account_list':
            encrypted_b64 = string_elem.text
            encrypted_data = base64.b64decode(encrypted_b64)
            return encrypted_data
    return None

def generate_key_variations(password):
    """生成密码的各种变体"""
    variations = [password]
    
    # 添加已知值的组合
    for key, value in KNOWN_VALUES.items():
        variations.extend([
            password + value,
            value + password,
            password + "_" + value,
            value + "_" + password,
        ])
    
    # 添加常见后缀
    common_suffixes = ["123", "2021", "epik", "wallet", ""]
    for suffix in common_suffixes:
        variations.append(password + suffix)
    
    return list(set(variations))  # 去重

def try_key_derivation_methods(password_variant, salt_data):
    """尝试各种密钥派生方法"""
    keys = []
    
    # 1. 直接SHA256
    keys.append(("SHA256", hashlib.sha256(password_variant.encode()).digest()))
    
    # 2. MD5 (老旧但可能使用)
    keys.append(("MD5", hashlib.md5(password_variant.encode()).digest()[:32]))
    
    # 3. PBKDF2 with different iterations
    for iterations in [1000, 4096, 10000, 100000]:
        try:
            key = PBKDF2(password_variant, salt_data, 32, count=iterations)
            keys.append((f"PBKDF2-{iterations}", key))
        except:
            pass
    
    # 4. Scrypt with different parameters
    scrypt_params = [
        (1024, 1, 1),
        (4096, 8, 1), 
        (16384, 8, 1),
        (32768, 8, 1)
    ]
    for N, r, p in scrypt_params:
        try:
            key = scrypt(password_variant.encode(), salt_data, 32, N=N, r=r, p=p)
            keys.append((f"Scrypt-{N}-{r}-{p}", key))
        except:
            pass
    
    # 5. HMAC variations
    for hash_func in [hashlib.sha256, hashlib.sha1, hashlib.md5]:
        try:
            key = hmac.new(password_variant.encode(), salt_data, hash_func).digest()[:32]
            keys.append((f"HMAC-{hash_func().name}", key))
        except:
            pass
    
    # 6. Custom key derivation (password + salt hashed multiple times)
    for rounds in [1, 10, 100, 1000]:
        try:
            key = password_variant.encode() + salt_data
            for _ in range(rounds):
                key = hashlib.sha256(key).digest()
            keys.append((f"Custom-SHA256-{rounds}", key[:32]))
        except:
            pass
    
    return keys

def try_decryption_methods(key, encrypted_data, method_name):
    """尝试各种解密方法"""
    results = []
    
    # 尝试不同的盐值长度
    salt_lengths = [0, 8, 16, 32]
    
    for salt_len in salt_lengths:
        if len(encrypted_data) <= salt_len:
            continue
            
        if salt_len == 0:
            salt = b""
            ciphertext = encrypted_data
        else:
            salt = encrypted_data[:salt_len]
            ciphertext = encrypted_data[salt_len:]
        
        # AES modes
        aes_modes = [
            ("CBC", AES.MODE_CBC),
            ("ECB", AES.MODE_ECB),
            ("CFB", AES.MODE_CFB),
            ("OFB", AES.MODE_OFB)
        ]
        
        for mode_name, mode in aes_modes:
            try:
                if mode == AES.MODE_ECB:
                    # ECB不需要IV
                    cipher = AES.new(key[:32], mode)
                    decrypted = cipher.decrypt(ciphertext)
                else:
                    # 其他模式需要IV
                    if len(ciphertext) < 16:
                        continue
                    iv = ciphertext[:16]
                    actual_ciphertext = ciphertext[16:]
                    
                    cipher = AES.new(key[:32], mode, iv)
                    decrypted = cipher.decrypt(actual_ciphertext)
                
                # 尝试去除填充
                for padding_method in ["pkcs7", "none"]:
                    try:
                        if padding_method == "pkcs7":
                            final_data = unpad(decrypted, AES.block_size)
                        else:
                            final_data = decrypted
                        
                        results.append((
                            f"{method_name}-AES-{mode_name}-salt{salt_len}-{padding_method}",
                            final_data
                        ))
                    except:
                        pass
            except:
                pass
    
    return results

def analyze_decrypted_data(data, method_name):
    """分析解密后的数据"""
    try:
        # 检查是否为有效的UTF-8文本
        try:
            text = data.decode('utf-8')
            if len(text) > 10 and all(c.isprintable() or c.isspace() for c in text):
                print(f"\n🔍 {method_name}: 解密为文本")
                print(f"长度: {len(text)} 字符")
                print(f"预览: {text[:200]}...")
                
                # 检查是否包含目标地址
                if TARGET_ADDRESS.lower() in text.lower():
                    print(f"🎯 找到目标地址!")
                    return True, text
                
                # 检查是否为JSON
                try:
                    json_data = json.loads(text)
                    print(f"✅ 有效JSON数据")
                    return analyze_json_data(json_data, method_name)
                except:
                    pass
                
                return False, text
        except:
            pass
        
        # 检查是否为JSON (即使有编码问题)
        if data.startswith(b'{') or data.startswith(b'['):
            try:
                # 尝试不同的编码
                for encoding in ['utf-8', 'latin1', 'cp1252']:
                    try:
                        text = data.decode(encoding)
                        json_data = json.loads(text)
                        print(f"\n🔍 {method_name}: JSON数据 (编码: {encoding})")
                        return analyze_json_data(json_data, method_name)
                    except:
                        continue
            except:
                pass
        
        # 二进制数据分析 - 搜索私钥模式
        print(f"\n🔍 {method_name}: 二进制数据分析")
        print(f"长度: {len(data)} 字节")
        
        # 搜索32字节的私钥候选
        private_keys_found = []
        for i in range(len(data) - 31):
            candidate = data[i:i+32]
            candidate_hex = binascii.hexlify(candidate).decode()
            
            # 检查是否为有效的私钥格式
            if all(c in '0123456789abcdef' for c in candidate_hex.lower()):
                address = private_key_to_address(candidate_hex)
                if address:
                    private_keys_found.append((candidate_hex, address))
                    if address.lower() == TARGET_ADDRESS.lower():
                        print(f"🎯 在偏移 {i} 找到目标私钥: {candidate_hex}")
                        return True, {"private_key": candidate_hex, "address": address}
        
        if private_keys_found:
            print(f"发现 {len(private_keys_found)} 个可能的私钥")
            for pk, addr in private_keys_found[:5]:  # 只显示前5个
                print(f"  {pk[:16]}... -> {addr}")
        
        return False, data
        
    except Exception as e:
        print(f"分析数据时出错: {e}")
        return False, None

def analyze_json_data(json_data, method_name):
    """分析JSON数据"""
    print(f"JSON类型: {type(json_data)}")
    
    if isinstance(json_data, dict):
        print(f"JSON键: {list(json_data.keys())}")
    elif isinstance(json_data, list):
        print(f"JSON数组长度: {len(json_data)}")
    
    # 转换为字符串进行搜索
    json_str = json.dumps(json_data, ensure_ascii=False)
    
    # 搜索目标地址
    if TARGET_ADDRESS.lower() in json_str.lower():
        print(f"🎯 在JSON中找到目标地址!")
        return True, json_data
    
    # 搜索私钥模式
    import re
    hex_64_pattern = re.findall(r'[0-9a-fA-F]{64}', json_str)
    private_keys_found = []
    
    for pattern in hex_64_pattern:
        address = private_key_to_address(pattern)
        if address:
            private_keys_found.append((pattern, address))
            if address.lower() == TARGET_ADDRESS.lower():
                print(f"🎯 在JSON中找到目标私钥: {pattern}")
                return True, {"private_key": pattern, "address": address}
    
    if private_keys_found:
        print(f"在JSON中发现 {len(private_keys_found)} 个可能的私钥")
        for pk, addr in private_keys_found[:3]:
            print(f"  {pk[:16]}... -> {addr}")
    
    return False, json_data

def main():
    """主函数 - 系统性暴力破解"""
    print("=" * 80)
    print("EpiK钱包综合解密攻击工具")
    print("采用逆向工程和暴力破解方法")
    print("=" * 80)
    
    # 提取加密数据
    encrypted_data = extract_encrypted_data()
    if not encrypted_data:
        print("错误: 无法提取加密数据")
        return
    
    print(f"加密数据长度: {len(encrypted_data)} 字节")
    print(f"数据开头: {binascii.hexlify(encrypted_data[:32]).decode()}")
    print(f"数据结尾: {binascii.hexlify(encrypted_data[-32:]).decode()}")
    
    success_results = []
    total_attempts = 0
    
    # 对每个密码进行系统性攻击
    for password in WALLET_PASSWORDS:
        print(f"\n{'='*60}")
        print(f"攻击密码: {password}")
        print(f"{'='*60}")
        
        # 生成密码变体
        password_variants = generate_key_variations(password)
        print(f"生成了 {len(password_variants)} 个密码变体")
        
        for variant in password_variants:
            print(f"\n尝试密码变体: {variant}")
            
            # 尝试不同的盐值提取方式
            salt_methods = [
                ("前16字节", encrypted_data[:16]),
                ("前32字节", encrypted_data[:32]),
                ("固定盐", b"epik_protocol_salt"),
                ("密码作为盐", variant.encode()),
                ("无盐", b"")
            ]
            
            for salt_name, salt_data in salt_methods:
                # 生成各种密钥
                keys = try_key_derivation_methods(variant, salt_data)
                
                for key_method, key in keys:
                    # 尝试解密
                    decryption_results = try_decryption_methods(key, encrypted_data, f"{variant}-{salt_name}-{key_method}")
                    
                    for method_name, decrypted_data in decryption_results:
                        total_attempts += 1
                        if total_attempts % 100 == 0:
                            print(f"已尝试 {total_attempts} 种方法...")
                        
                        found_target, parsed_data = analyze_decrypted_data(decrypted_data, method_name)
                        if found_target:
                            success_results.append({
                                "password": password,
                                "variant": variant,
                                "method": method_name,
                                "data": parsed_data
                            })
                            print(f"🎉 成功解密!")
    
    # 输出最终结果
    print(f"\n{'='*80}")
    print(f"攻击完成! 总共尝试了 {total_attempts} 种方法")
    
    if success_results:
        print("🎉 解密成功!")
        for result in success_results:
            print(f"密码: {result['password']}")
            print(f"变体: {result['variant']}")
            print(f"方法: {result['method']}")
            print(f"数据: {result['data']}")
        
        # 保存结果
        with open("successful_decryption_results.json", "w") as f:
            json.dump(success_results, f, indent=2, ensure_ascii=False)
        print("结果已保存到: successful_decryption_results.json")
    else:
        print("❌ 所有攻击方法都失败了")
        print("\n可能的原因:")
        print("1. 密码确实不正确")
        print("2. 使用了我们未考虑到的加密算法")
        print("3. 需要硬件安全模块或其他外部因素")
        print("4. 数据可能已损坏")
        
        print(f"\n建议:")
        print("1. 仔细回忆是否还有其他可能的密码")
        print("2. 检查是否有密码提示或备份")
        print("3. 考虑寻找专业的数字取证服务")
    
    print(f"{'='*80}")

if __name__ == "__main__":
    main()
