#!/usr/bin/env python3
"""
精确加密数据搜索
在912字节的加密账户数据中精确搜索目标地址和私钥
"""

import base64
import json
import xml.etree.ElementTree as ET
import hashlib
import binascii
import re
import os
from ecdsa import SigningKey, SECP256k1
from Crypto.Hash import keccak

TARGET_ADDRESS = "******************************************"

def extract_and_analyze_encrypted_data():
    """提取并分析加密数据"""
    print("=== 提取加密账户数据 ===")
    
    prefs_file = "epik_data_final/apps/com.epik.wallet/sp/FlutterSharedPreferences.xml"
    tree = ET.parse(prefs_file)
    root = tree.getroot()
    
    for string_elem in root.findall('string'):
        name = string_elem.get('name')
        if name == 'flutter.am_account_list':
            encrypted_b64 = string_elem.text
            encrypted_data = base64.b64decode(encrypted_b64)
            
            print(f"加密数据长度: {len(encrypted_data)} 字节")
            
            # 转换为十六进制字符串进行搜索
            hex_data = binascii.hexlify(encrypted_data).decode().lower()
            print(f"十六进制数据长度: {len(hex_data)} 字符")
            
            # 搜索目标地址的不同表示形式
            search_address_in_hex_data(hex_data, encrypted_data)
            
            # 详细分析数据结构
            analyze_data_structure(encrypted_data)
            
            return encrypted_data
    
    return None

def search_address_in_hex_data(hex_data, binary_data):
    """在十六进制数据中搜索目标地址"""
    print(f"\n=== 搜索目标地址 ===")
    
    # 目标地址的不同表示形式
    target_patterns = [
        TARGET_ADDRESS[2:].lower(),  # 去掉0x前缀
        TARGET_ADDRESS[2:].upper(),  # 大写
        TARGET_ADDRESS.lower(),      # 带0x前缀小写
        TARGET_ADDRESS.upper(),      # 带0x前缀大写
    ]
    
    print(f"搜索模式:")
    for i, pattern in enumerate(target_patterns):
        print(f"  {i+1}. {pattern}")
    
    found_positions = []
    
    for i, pattern in enumerate(target_patterns):
        positions = []
        start = 0
        while True:
            pos = hex_data.find(pattern, start)
            if pos == -1:
                break
            positions.append(pos)
            start = pos + 1
        
        if positions:
            print(f"\n*** 找到模式 {i+1} 在位置: {positions} ***")
            found_positions.extend([(pos, pattern) for pos in positions])
    
    # 分析找到的位置
    for pos, pattern in found_positions:
        byte_pos = pos // 2
        print(f"\n分析位置 {pos} (字节位置 {byte_pos}):")
        print(f"模式: {pattern}")
        
        # 提取周围的数据
        context_start = max(0, byte_pos - 50)
        context_end = min(len(binary_data), byte_pos + 50)
        context = binary_data[context_start:context_end]
        
        print(f"周围数据 (字节 {context_start}-{context_end}):")
        print(f"十六进制: {binascii.hexlify(context).decode()}")
        
        # 检查是否有私钥在附近
        search_private_key_near_address(binary_data, byte_pos)
    
    if not found_positions:
        print("未在加密数据中找到目标地址")
        print("可能原因:")
        print("1. 地址被进一步编码或加密")
        print("2. 地址以不同的格式存储")
        print("3. 数据结构复杂，需要特殊解析")

def search_private_key_near_address(data, address_pos):
    """在地址附近搜索私钥"""
    print(f"\n--- 在地址位置 {address_pos} 附近搜索私钥 ---")
    
    # 检查地址前后的32字节区域
    search_ranges = [
        (address_pos - 64, address_pos - 32, "地址前32字节"),
        (address_pos - 32, address_pos, "地址紧前32字节"),
        (address_pos + 20, address_pos + 52, "地址后32字节"),
        (address_pos + 52, address_pos + 84, "地址远后32字节"),
    ]
    
    for start, end, description in search_ranges:
        if start >= 0 and end <= len(data):
            candidate_bytes = data[start:end]
            if len(candidate_bytes) == 32:
                candidate_hex = binascii.hexlify(candidate_bytes).decode()
                
                print(f"{description}: {candidate_hex}")
                
                # 验证是否是有效私钥
                if is_valid_private_key(candidate_hex):
                    address = generate_ethereum_address(candidate_hex)
                    print(f"  生成的地址: {address}")
                    
                    if address and address.lower() == TARGET_ADDRESS.lower():
                        print(f"  *** 找到匹配的私钥！{candidate_hex} ***")
                        return candidate_hex
                else:
                    print(f"  不是有效的私钥")

def analyze_data_structure(data):
    """分析数据结构"""
    print(f"\n=== 数据结构分析 ===")
    
    print(f"数据长度: {len(data)} 字节")
    
    # 显示数据的不同部分
    sections = [
        (0, 32, "前32字节"),
        (32, 64, "第2个32字节"),
        (64, 96, "第3个32字节"),
        (len(data)-96, len(data)-64, "倒数第3个32字节"),
        (len(data)-64, len(data)-32, "倒数第2个32字节"),
        (len(data)-32, len(data), "最后32字节"),
    ]
    
    for start, end, description in sections:
        if start >= 0 and end <= len(data):
            section_data = data[start:end]
            hex_data = binascii.hexlify(section_data).decode()
            
            print(f"\n{description} (字节 {start}-{end}):")
            print(f"  十六进制: {hex_data}")
            
            # 检查是否可能是私钥
            if len(section_data) == 32 and is_valid_private_key(hex_data):
                address = generate_ethereum_address(hex_data)
                print(f"  如果是私钥，生成地址: {address}")
                
                if address and address.lower() == TARGET_ADDRESS.lower():
                    print(f"  *** 找到匹配的私钥！{hex_data} ***")
                    return hex_data
            
            # 检查是否包含可读文本
            try:
                text = section_data.decode('utf-8', errors='ignore')
                if any(c.isalpha() for c in text):
                    print(f"  可能的文本: {text}")
            except:
                pass

def is_valid_private_key(hex_str):
    """检查是否是有效的私钥"""
    try:
        if len(hex_str) != 64:
            return False
        
        private_key_int = int(hex_str, 16)
        
        # 检查范围
        if private_key_int == 0 or private_key_int >= 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD0364141:
            return False
        
        # 检查随机性
        unique_chars = len(set(hex_str.lower()))
        if unique_chars < 8:
            return False
        
        return True
    except:
        return False

def generate_ethereum_address(private_key_hex):
    """生成以太坊地址"""
    try:
        private_key_bytes = binascii.unhexlify(private_key_hex)
        sk = SigningKey.from_string(private_key_bytes, curve=SECP256k1)
        public_key = sk.get_verifying_key().to_string()
        
        keccak_hash = keccak.new(digest_bits=256)
        keccak_hash.update(public_key)
        address_bytes = keccak_hash.digest()[-20:]
        
        return "0x" + binascii.hexlify(address_bytes).decode().lower()
    except:
        return None

def exhaustive_private_key_search(data):
    """穷尽搜索所有可能的私钥"""
    print(f"\n=== 穷尽私钥搜索 ===")
    print(f"搜索 {len(data) - 31} 个可能的32字节序列...")
    
    matches = []
    valid_keys_count = 0
    
    for i in range(len(data) - 31):
        candidate_bytes = data[i:i+32]
        candidate_hex = binascii.hexlify(candidate_bytes).decode()
        
        if is_valid_private_key(candidate_hex):
            valid_keys_count += 1
            
            if valid_keys_count % 10 == 0:
                print(f"已检查 {valid_keys_count} 个有效私钥候选...")
            
            address = generate_ethereum_address(candidate_hex)
            if address and address.lower() == TARGET_ADDRESS.lower():
                print(f"*** 在偏移量 {i} 找到匹配的私钥: {candidate_hex} ***")
                print(f"*** 生成的地址: {address} ***")
                matches.append({
                    'private_key': candidate_hex,
                    'address': address,
                    'offset': i
                })
    
    print(f"总共检查了 {valid_keys_count} 个有效的私钥候选")
    return matches

def main():
    """主函数"""
    print("精确加密数据搜索工具")
    print(f"目标地址: {TARGET_ADDRESS}")
    print("=" * 80)
    
    # 提取并分析加密数据
    encrypted_data = extract_and_analyze_encrypted_data()
    
    if encrypted_data:
        # 穷尽搜索私钥
        matches = exhaustive_private_key_search(encrypted_data)
        
        print(f"\n" + "=" * 80)
        if matches:
            print(f"*** 成功！找到 {len(matches)} 个匹配的私钥！***")
            
            for match in matches:
                print(f"\n🔑 私钥: {match['private_key']}")
                print(f"📍 地址: {match['address']}")
                print(f"📍 偏移量: {match['offset']}")
            
            # 保存结果
            with open("found_private_key_final.json", "w") as f:
                json.dump({
                    'target_address': TARGET_ADDRESS,
                    'matches': matches
                }, f, indent=2)
            
            print(f"\n✅ 结果已保存到: found_private_key_final.json")
            
        else:
            print("❌ 未找到匹配的私钥")
            print("数据可能需要进一步解密或使用不同的地址生成算法")
    
    else:
        print("未找到加密数据")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
