#!/usr/bin/env python3
"""
EpiK钱包数据结构分析工具
详细分析epik_data_final中的所有数据
"""

import os
import json
import xml.etree.ElementTree as ET
import sqlite3
import base64
import binascii
from pathlib import Path

def analyze_data_structure():
    """分析EpiK钱包的完整数据结构"""
    print("=" * 80)
    print("EpiK Portal 钱包数据结构分析")
    print("=" * 80)
    
    base_path = "epik_data_final/apps/com.epik.wallet"
    
    structure_analysis = {
        "应用基本信息": analyze_app_info(base_path),
        "SharedPreferences配置": analyze_shared_preferences(base_path),
        "数据库文件": analyze_databases(base_path),
        "应用文件": analyze_app_files(base_path),
        "缓存和临时文件": analyze_cache_files(base_path),
        "运行时数据": analyze_runtime_data(base_path)
    }
    
    # 输出分析结果
    for category, data in structure_analysis.items():
        print(f"\n{'='*60}")
        print(f"📁 {category}")
        print(f"{'='*60}")
        
        if isinstance(data, dict):
            for key, value in data.items():
                print(f"\n📄 {key}:")
                if isinstance(value, dict):
                    for subkey, subvalue in value.items():
                        print(f"  • {subkey}: {subvalue}")
                elif isinstance(value, list):
                    for item in value:
                        print(f"  • {item}")
                else:
                    print(f"  {value}")
        else:
            print(data)
    
    return structure_analysis

def analyze_app_info(base_path):
    """分析应用基本信息"""
    info = {}
    
    # APK文件
    apk_path = os.path.join(base_path, "a/base.apk")
    if os.path.exists(apk_path):
        size = os.path.getsize(apk_path)
        info["APK文件"] = {
            "路径": "a/base.apk",
            "大小": f"{size:,} 字节 ({size/1024/1024:.1f} MB)",
            "说明": "EpiK Portal应用的安装包"
        }
    
    # Manifest文件
    manifest_path = os.path.join(base_path, "_manifest")
    if os.path.exists(manifest_path):
        try:
            with open(manifest_path, 'r') as f:
                content = f.read()
            info["Manifest"] = {
                "路径": "_manifest",
                "内容": content[:200] + "..." if len(content) > 200 else content,
                "说明": "应用清单文件"
            }
        except:
            info["Manifest"] = {"路径": "_manifest", "状态": "读取失败"}
    
    return info

def analyze_shared_preferences(base_path):
    """分析SharedPreferences配置文件"""
    sp_path = os.path.join(base_path, "sp")
    prefs = {}
    
    if not os.path.exists(sp_path):
        return {"状态": "SharedPreferences目录不存在"}
    
    for file in os.listdir(sp_path):
        if file.endswith('.xml'):
            file_path = os.path.join(sp_path, file)
            prefs[file] = analyze_xml_preferences(file_path)
    
    return prefs

def analyze_xml_preferences(file_path):
    """分析单个XML配置文件"""
    try:
        tree = ET.parse(file_path)
        root = tree.getroot()
        
        analysis = {
            "文件大小": f"{os.path.getsize(file_path)} 字节",
            "配置项数量": len(root),
            "配置内容": {}
        }
        
        for elem in root:
            name = elem.get('name', '未知')
            value = elem.text or ""
            
            # 分析配置项的重要性
            importance = classify_config_importance(name, value)
            
            analysis["配置内容"][name] = {
                "类型": elem.tag,
                "值长度": len(value),
                "重要性": importance,
                "值预览": value[:100] + "..." if len(value) > 100 else value
            }
        
        return analysis
        
    except Exception as e:
        return {"错误": str(e)}

def classify_config_importance(name, value):
    """分类配置项的重要性"""
    name_lower = name.lower()
    
    # 钱包核心数据
    if any(keyword in name_lower for keyword in ['account', 'wallet', 'private', 'key', 'mnemonic', 'seed']):
        return "🔴 钱包核心数据"
    
    # 矿工相关
    if 'miner' in name_lower or name_lower.startswith('flutter.miner'):
        return "🟡 矿工相关数据"
    
    # 交易相关
    if 'tx' in name_lower or 'transaction' in name_lower:
        return "🟡 交易相关数据"
    
    # 服务器配置
    if 'server' in name_lower or 'config' in name_lower:
        return "🟢 服务器配置"
    
    # 统计和分析
    if any(keyword in name_lower for keyword in ['umeng', 'analytics', 'probe', 'stat']):
        return "🔵 统计分析数据"
    
    # WebView相关
    if 'webview' in name_lower:
        return "🔵 WebView配置"
    
    return "⚪ 其他配置"

def analyze_databases(base_path):
    """分析数据库文件"""
    db_path = os.path.join(base_path, "db")
    databases = {}
    
    if not os.path.exists(db_path):
        return {"状态": "数据库目录不存在"}
    
    for file in os.listdir(db_path):
        if file.endswith('.db'):
            file_path = os.path.join(db_path, file)
            databases[file] = analyze_sqlite_database(file_path)
    
    return databases

def analyze_sqlite_database(file_path):
    """分析SQLite数据库"""
    try:
        conn = sqlite3.connect(file_path)
        cursor = conn.cursor()
        
        # 获取所有表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        analysis = {
            "文件大小": f"{os.path.getsize(file_path)} 字节",
            "表数量": len(tables),
            "表详情": {}
        }
        
        for table_name in tables:
            table = table_name[0]
            
            # 获取表结构
            cursor.execute(f"PRAGMA table_info({table});")
            columns = cursor.fetchall()
            
            # 获取行数
            cursor.execute(f"SELECT COUNT(*) FROM {table};")
            row_count = cursor.fetchone()[0]
            
            analysis["表详情"][table] = {
                "列数": len(columns),
                "行数": row_count,
                "列信息": [{"名称": col[1], "类型": col[2]} for col in columns]
            }
            
            # 如果行数不多，显示一些示例数据
            if row_count > 0 and row_count <= 10:
                cursor.execute(f"SELECT * FROM {table} LIMIT 3;")
                sample_data = cursor.fetchall()
                analysis["表详情"][table]["示例数据"] = sample_data
        
        conn.close()
        return analysis
        
    except Exception as e:
        return {"错误": str(e)}

def analyze_app_files(base_path):
    """分析应用文件"""
    f_path = os.path.join(base_path, "f")
    files = {}
    
    if not os.path.exists(f_path):
        return {"状态": "应用文件目录不存在"}
    
    for root, dirs, filenames in os.walk(f_path):
        for filename in filenames:
            file_path = os.path.join(root, filename)
            rel_path = os.path.relpath(file_path, f_path)
            
            files[rel_path] = analyze_single_file(file_path)
    
    return files

def analyze_single_file(file_path):
    """分析单个文件"""
    try:
        size = os.path.getsize(file_path)
        
        analysis = {
            "大小": f"{size} 字节",
            "类型": "未知"
        }
        
        # 根据文件扩展名和内容判断类型
        if file_path.endswith('.json'):
            analysis["类型"] = "JSON配置文件"
            try:
                with open(file_path, 'r') as f:
                    content = f.read()
                json_data = json.loads(content)
                analysis["JSON内容"] = str(json_data)[:200] + "..."
            except:
                pass
        
        elif file_path.endswith('.dat'):
            analysis["类型"] = "数据文件"
            try:
                with open(file_path, 'rb') as f:
                    content = f.read(100)  # 读取前100字节
                analysis["十六进制预览"] = binascii.hexlify(content).decode()
            except:
                pass
        
        elif file_path.endswith('.cache'):
            analysis["类型"] = "缓存文件"
        
        else:
            # 尝试判断是文本还是二进制
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read(200)
                analysis["类型"] = "文本文件"
                analysis["内容预览"] = content
            except:
                analysis["类型"] = "二进制文件"
        
        return analysis
        
    except Exception as e:
        return {"错误": str(e)}

def analyze_cache_files(base_path):
    """分析缓存文件"""
    cache_info = {}
    
    # 检查各种缓存目录
    cache_dirs = ["r", "ef"]
    
    for cache_dir in cache_dirs:
        cache_path = os.path.join(base_path, cache_dir)
        if os.path.exists(cache_path):
            cache_info[cache_dir] = analyze_cache_directory(cache_path)
    
    return cache_info

def analyze_cache_directory(cache_path):
    """分析缓存目录"""
    analysis = {
        "目录大小": calculate_directory_size(cache_path),
        "文件数量": count_files_in_directory(cache_path),
        "子目录": []
    }
    
    for item in os.listdir(cache_path):
        item_path = os.path.join(cache_path, item)
        if os.path.isdir(item_path):
            analysis["子目录"].append({
                "名称": item,
                "文件数": count_files_in_directory(item_path),
                "大小": calculate_directory_size(item_path)
            })
    
    return analysis

def analyze_runtime_data(base_path):
    """分析运行时数据"""
    runtime_info = {
        "总体统计": {
            "总文件数": count_files_in_directory(base_path),
            "总大小": calculate_directory_size(base_path),
            "目录结构深度": calculate_max_depth(base_path)
        }
    }
    
    return runtime_info

def calculate_directory_size(directory):
    """计算目录大小"""
    total_size = 0
    for root, dirs, files in os.walk(directory):
        for file in files:
            file_path = os.path.join(root, file)
            try:
                total_size += os.path.getsize(file_path)
            except:
                pass
    return f"{total_size:,} 字节 ({total_size/1024/1024:.1f} MB)"

def count_files_in_directory(directory):
    """计算目录中的文件数量"""
    count = 0
    for root, dirs, files in os.walk(directory):
        count += len(files)
    return count

def calculate_max_depth(directory):
    """计算目录的最大深度"""
    max_depth = 0
    for root, dirs, files in os.walk(directory):
        depth = root[len(directory):].count(os.sep)
        max_depth = max(max_depth, depth)
    return max_depth

def main():
    """主函数"""
    if not os.path.exists("epik_data_final/apps/com.epik.wallet"):
        print("错误: 找不到EpiK钱包数据目录")
        return
    
    analysis = analyze_data_structure()
    
    # 保存分析结果
    with open("epik_data_structure_report.json", "w", encoding='utf-8') as f:
        json.dump(analysis, f, indent=2, ensure_ascii=False)
    
    print(f"\n{'='*80}")
    print("📊 分析完成！详细报告已保存到: epik_data_structure_report.json")
    print(f"{'='*80}")

if __name__ == "__main__":
    main()
